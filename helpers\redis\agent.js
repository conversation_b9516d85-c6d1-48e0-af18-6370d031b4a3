const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");

// Function to get one data from Redis
const getOneAgentRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_AGENT}_${hotelId}:${id}`);
        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all data from Redis
const getAllAgentRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_AGENT}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set single data in Redis
const setOneAgentRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneAgentRedis(hotelId, data._id);

            // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
            await addHashValues(`${process.env.HASH_AGENT}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    description: data.description,
                    isEnable: data.isEnable,
                    // embedding: vectorBuffer,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set multiple data in Redis
const setAllAgentRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllAgentRedis(hotelId);

            dataArray.map(async (data) => {
                // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
                await addHashValues(`${process.env.HASH_AGENT}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        description: data.description,
                        isEnable: data.isEnable,
                        // embedding: vectorBuffer,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete single data from Redis
const delOneAgentRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_AGENT}_${hotelId}:${id}`)
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete all data from Redis
const delAllAgentRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_AGENT}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);
        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey)
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = { getOneAgentRedis, getAllAgentRedis, setOneAgentRedis, setAllAgentRedis, delOneAgentRedis, delAllAgentRedis };