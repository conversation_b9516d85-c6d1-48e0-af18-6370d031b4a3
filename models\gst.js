const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;

const gstSchema = new Schema(
  {
    minTariff: {
      type: Number,
      required: true,
      min: 0
    },
    maxTariff: {
      type: Number,
      required: true,

    },
    cGSTPercentage: {
      type: Number,
      required: true,
      min: 0
    },
    sGSTPercentage: {
      type: Number,
      required: true,
      min: 0
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("GST", gstSchema);