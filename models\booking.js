const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const { ROOM_STATUS } = require("../configs/roomOptions");

const Schema = mongoose.Schema;

const roomSchema = new Schema({
  room: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "Room"
  },
  guestCount: {
    type: Number,
    required: true,
    min: 1
  },
  extraPersonCount: {
    type: Number,
    default: 0,
    min: 0
  },
  extraBedCount: {
    type: Number,
    default: 0,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  tariff: {
    type: Number,
    required: true,
    min: 0
  },
  cGSTPercentage: {
    type: Number,
    default: 0,
    min: 0
  },
  sGSTPercentage: {
    type: Number,
    default: 0,
    min: 0
  },
  cGSTAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  sGSTAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  occupancyDate: {
    type: Date,
    required: true
  },
  actualAccommodation: {
    type: Number,
    default: 1
  },
  actualExtraPersonTariff: {
    type: Number,
    default: 0,
  },
  actualExtraBedTariff: {
    type: Number,
    default: 0
  },
  actualMaxDiscount: {
    type: Number,
    default: 0
  },
  actualTariff: {
    type: Number,
    default: 0
  },
  breakfastGuestCount: {
    type: Number,
    default: 0
  }
});

const bookingSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    employees: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "Employee"
      }
    ],
    guest: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Guest"
    },
    plan: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Plan"
    },
    agent: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Agent"
    },
    roomNos: {
      type: String,
      required: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    dueAmount: {
      type: Number
    },
    billNo: {
      type: Number,
      default: null
    },
    rooms: [roomSchema],
    status: {
      type: String,
      default: ROOM_STATUS.BOOKED
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Booking", bookingSchema);