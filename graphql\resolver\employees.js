const { mongoEmployees, mongoSearchEmployee, mongoGetEmployee, mongoAddEmployee, mongoModEmployee, mongoDelEmployee, mongoDelEmployees } = require("./mongo/employeeMongo");
const { redisEmployees, redisSearchEmployee, redisGetEmployee, redisSetEmployee, redisDelEmployee } = require("./redis/employeeRedis");
const { employeeDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "employees.js";

// get all documents of a collection
const employees = async (args, req) => {
  const FUNCTION_NAME = "employees";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoEmployees(hotelId, employeeId);
    }
    else {
      allObjects = await redisEmployees(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await employeeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchEmployee = async (args, req) => {
  const FUNCTION_NAME = "searchEmployee";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.employeeSearchInput.trim().toUpperCase();

  let searchArray = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchArray = await mongoSearchEmployee(hotelId, employeeId, searchKey);
    }
    else {
      searchArray = await redisSearchEmployee(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_SEARCH);

    // return output
    return searchArray.map(async (object) => {
      return await employeeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getEmployee = async (args, req) => {
  const FUNCTION_NAME = "getEmployee";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetEmployee(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetEmployee(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_GET);

    // return output
    return await employeeDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addEmployee = async (args, req) => {
  const FUNCTION_NAME = "addEmployee";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { role, name, address, mobile, email } = args.employeeInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (role === undefined) throw new customError(ERR_MSG.INVALID_ROLE, ERR_CODE.BAD_REQUEST);
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (mobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (email === "") throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.BAD_REQUEST);

    const _role = role;
    const _name = name.trim().toUpperCase();
    const _address = address ? address.trim().toUpperCase() : "";
    const _mobile = mobile.trim();
    const _email = email.trim().toLowerCase();

    // add data
    const addObject = await mongoAddEmployee(hotelId, employeeId, _role, _name, _address, _mobile, _email);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetEmployee(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_ADD);

    return await employeeDetail(addObject);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modEmployee = async (args, req) => {
  const FUNCTION_NAME = "modEmployee";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, role, name, address, mobile, email } = args.employeeInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (role === "") throw new customError(ERR_MSG.INVALID_ROLE, ERR_CODE.BAD_REQUEST);
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (mobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (email === "") throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.BAD_REQUEST);

    const _role = role;
    const _name = name.trim().toUpperCase();
    const _address = address ? address.trim() : "";
    const _mobile = mobile.trim();
    const _email = email.trim().toLowerCase();

    // modify data
    const modObject = await mongoModEmployee(hotelId, employeeId, _id, _role, _name, _address, _mobile, _email);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetEmployee(hotelId, employeeId, modObject);
    }

    // return output
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_MOD);

    // return output
    return await employeeDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delEmployee = async (args, req) => {
  const FUNCTION_NAME = "delEmployee";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args.employeeId;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelEmployee(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelEmployee(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEE_DEL);

    // return output
    return await employeeDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delEmployees = async (args, req) => {
  const FUNCTION_NAME = "delEmployees";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.employeesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.EMPLOYEE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelEmployees(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelEmployee(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.EMPLOYEES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await employeeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { employees, searchEmployee, getEmployee, addEmployee, modEmployee, delEmployee, delEmployees };