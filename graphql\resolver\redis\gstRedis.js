const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneGSTRedis } = require("../../../helpers/redis/gst");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "gsts.js";

// get all documents of a collection
const redisGSTs = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisGSTs";

  try {
    // get all data
    const query = `*`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_GST_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'minTariff', 'maxTariff', 'cGSTPercentage', 'sGSTPercentage',
      'SORTBY', 'minTariff', 'ASC',
      'LIMIT', '0', '10'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchGST = async (hotelId, employeeId, searchTariff) => {
  const FUNCTION_NAME = "redisSearchGST";

  try {
    // get all data
    const query = `*`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_GST_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'minTariff', 'maxTariff', 'cGSTPercentage', 'sGSTPercentage',
      'SORTBY', 'minTariff', 'ASC',
      'LIMIT', '0', '10'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchTariff) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => parseFloat(item.minTariff) <= parseFloat(searchTariff) &&
          parseFloat(item.maxTariff) >= parseFloat(searchTariff));
        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const redisGetGST = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetGST";

  try {
    // read single data
    const query = `(@_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_GST_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'minTariff', 'maxTariff', 'cGSTPercentage', 'sGSTPercentage',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const redisSetGST = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetGST";

  try {
    // read single data
    await setOneGSTRedis(data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const redisDelGST = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelGST";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_GST}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisGSTs, redisSearchGST, redisGetGST, redisSetGST, redisDelGST };