const { mongoHotels, mongoSearchHotel, mongoGetHotel, mongoGetHotelFromUrl, mongoAddHotel, mongoModHotel, mongoDelHotel, mongoDelHotels } = require("./mongo/hotelMongo");
const { redisHotels, redisSearchHotel, redisGetHotel, redisSetHotel, redisDelHotel } = require("./redis/hotelRedis");
const { hotelDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "hotels.js";

// get all documents of a collection
const hotels = async (args, req) => {
  const FUNCTION_NAME = "hotels";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObject = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObject = await mongoHotels(hotelId, employeeId);
    }
    else {
      allObject = await redisHotels(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_LIST);

    // return output
    return allObject.map(async (object) => {
      return await hotelDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchHotel = async (args, req) => {
  const FUNCTION_NAME = "searchHotel";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.hotelSearchInput ? args.hotelSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchHotel(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchHotel(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await hotelDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getHotel = async (args, req) => {
  const _id = args._id;
  let getObject = null;

  try {
    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetHotel(_id);
    }
    else {
      getObject = await redisGetHotel(_id);
    }

    if (!getObject) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // return output
    return await hotelDetail(getObject);
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// find a document by place & name from a collection
const getHotelFromUrl = async (args, req) => {
  const { urlName, urlPlace } = args.urlHotelInput;

  try {
    const _urlName = urlName.trim().toUpperCase();
    const _urlPlace = urlPlace.trim().toUpperCase();

    // db
    const getObject = await mongoGetHotelFromUrl(_urlName, _urlPlace);
    if (!getObject) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    return await hotelDetail(getObject);
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addHotel = async (args, req) => {
  const FUNCTION_NAME = "addHotel";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, address, city, state, pin, phone, email, gstNo, urlName, urlPlace } = args.hotelInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (!address) throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.BAD_REQUEST);
    if (!city) throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);
    if (!state) throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.BAD_REQUEST);
    if (!pin) throw new customError(ERR_MSG.INVALID_PIN, ERR_CODE.BAD_REQUEST);
    if (!phone) throw new customError(ERR_MSG.INVALID_PHONE, ERR_CODE.BAD_REQUEST);
    if (!email) throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.BAD_REQUEST);
    if (!gstNo) throw new customError(ERR_MSG.INVALID_GST, ERR_CODE.BAD_REQUEST);
    if (!urlName) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (!urlPlace) throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _address = address.trim().toUpperCase();
    const _city = city.trim().toUpperCase();
    const _state = state.trim().toUpperCase();
    const _pin = pin.trim();
    const _phone = phone.trim();
    const _email = email.trim().toLowerCase();
    const _gstNo = gstNo.trim().toUpperCase();
    const _urlName = urlName.trim().toUpperCase();
    const _urlPlace = urlPlace.trim().toUpperCase();

    // add data
    const addObject = await mongoAddHotel(hotelId, employeeId, _name, _address, _city, _state,
      _pin, _phone, _email, _gstNo, _urlName, _urlPlace);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetHotel(employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_ADD);

    // return output
    return await hotelDetail(addObject);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modHotel = async (args, req) => {
  const FUNCTION_NAME = "modHotel";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, address, city, state, pin, phone, email, gstNo, urlName, urlPlace } = args.hotelInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (!address) throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.CONFLICT);
    if (!city) throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.CONFLICT);
    if (!state) throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.CONFLICT);
    if (!phone) throw new customError(ERR_MSG.INVALID_PHONE, ERR_CODE.CONFLICT);
    if (!email) throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.CONFLICT);
    if (!gstNo) throw new customError(ERR_MSG.INVALID_GST, ERR_CODE.BAD_REQUEST);
    if (!urlName) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (!urlPlace) throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _address = address.trim().toUpperCase();
    const _city = city.trim().toUpperCase();
    const _state = state.trim().toUpperCase();
    const _pin = pin.trim();
    const _phone = phone.trim();
    const _email = email.trim().toLowerCase();
    const _gstNo = gstNo.trim().toUpperCase();
    const _urlName = urlName.trim().toUpperCase();
    const _urlPlace = urlPlace.trim().toUpperCase();

    // modify data
    const modObject = await mongoModHotel(hotelId, employeeId, _id, _name, _address, _city, _state,
      _pin, _phone, _email, _gstNo, _urlName, _urlPlace);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetHotel(employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_MOD);

    // return output
    return await hotelDetail(modObject);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delHotel = async (args, req) => {
  const FUNCTION_NAME = "delHotel";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args.hotelId;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelHotel(_id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisDelHotel(delObject._id);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_DEL);

    // return output
    return await hotelDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delHotels = async (args, req) => {
  const FUNCTION_NAME = "delHotels";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.hotelsInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.HOTEL, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelHotels(_ids);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelHotel(object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.HOTEL_DEL);

    // return output
    return delObjects.map(async (object) => {
      return hotelDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { hotels, searchHotel, getHotel, getHotelFromUrl, addHotel, modHotel, delHotel, delHotels };