const { mongoPaymentModes, mongoSearchPaymentMode, mongoGetPaymentMode, mongoAddPaymentMode, mongoModPaymentMode, mongoDelPaymentMode, mongoDelPaymentModes } = require("./mongo/paymentModeMongo");
const { redisPaymentModes, redisSearchPaymentMode, redisGetPaymentMode, redisSetPaymentMode, redisDelPaymentMode } = require("./redis/paymentModeRedis");
const { paymentModeDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "paymentModes.js";

// get all documents of a collection
const paymentModes = async (args, req) => {
  const FUNCTION_NAME = "paymentModes";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let objectObject = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      objectObject = await mongoPaymentModes(hotelId, employeeId);
    }
    else {
      objectObject = await redisPaymentModes(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_LIST);

    // return output
    return objectObject.map(async (object) => {
      return await paymentModeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchPaymentMode = async (args, req) => {
  const FUNCTION_NAME = "searchPaymentMode";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.paymentModeSearchInput ? args.paymentModeSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchPaymentMode(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchPaymentMode(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await paymentModeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getPaymentMode = async (args, req) => {
  const FUNCTION_NAME = "getPaymentMode";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetPaymentMode(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetPaymentMode(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.PAYMENT_MODE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_GET);

    // return output
    return await paymentModeDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addPaymentMode = async (args, req) => {
  const FUNCTION_NAME = "addPaymentMode";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, description } = args.paymentModeInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddPaymentMode(hotelId, employeeId, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetPaymentMode(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_ADD);

    // return output
    return await paymentModeDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modPaymentMode = async (args, req) => {
  const FUNCTION_NAME = "modPaymentMode";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, description } = args.paymentModeInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModPaymentMode(hotelId, employeeId, _id, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetPaymentMode(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_MOD);

    // return output
    return await paymentModeDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delPaymentMode = async (args, req) => {
  const FUNCTION_NAME = "delPaymentMode";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelPaymentMode(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelPaymentMode(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MODE_DEL);

    // return output
    return await paymentModeDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delPaymentModes = async (args, req) => {
  const FUNCTION_NAME = "delPaymentModes";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.paymentModesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT_MODE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    const delObjects = await mongoDelPaymentModes(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelPaymentMode(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENTS_MODE_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await paymentModeDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  paymentModes, searchPaymentMode, getPaymentMode, addPaymentMode,
  modPaymentMode, delPaymentMode, delPaymentModes
};