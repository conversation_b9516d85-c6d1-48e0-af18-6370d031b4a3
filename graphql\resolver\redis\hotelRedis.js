const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneHotelRedis } = require("../../../helpers/redis/hotel");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "hotelRedis.js";

// get all documents of a collection
const redisHotels = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisHotels";

  try {
    // get all data
    const query = `(@isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_HOTEL_FILTER}`,
      query,
      'RETURN', '23', '_id', 'hotel', 'name', 'address', 'city', 'state', 'pin', 'phone', 'email', 'gstNo',
      'urlName', 'urlPlace', 'foodCGSTPercentage', 'foodSGSTPercentage', 'serviceCGSTPercentage', 'serviceSGSTPercentage',
      'serviceChargePercentage', 'lastKOTNo', 'lastSOTNo', 'lastMOTNo', 'lastFinalBillNo', 'lastFoodBillNo', 'lastServiceBillNo',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchHotel = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchHotel";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_HOTEL_FILTER}`,
      query,
      'RETURN', '23', '_id', 'hotel', 'name', 'address', 'city', 'state', 'pin', 'phone', 'email', 'gstNo',
      'urlName', 'urlPlace', 'foodCGSTPercentage', 'foodSGSTPercentage', 'serviceCGSTPercentage', 'serviceSGSTPercentage',
      'serviceChargePercentage', 'lastKOTNo', 'lastSOTNo', 'lastMOTNo', 'lastFinalBillNo', 'lastFoodBillNo', 'lastServiceBillNo',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const redisGetHotel = async (id) => {
  const FUNCTION_NAME = "redisGetHotel";

  try {
    // read single data
    const query = `(@isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_HOTEL_UNIQUE}`,
      query,
      'RETURN', '23', '_id', 'hotel', 'name', 'address', 'city', 'state', 'pin', 'phone', 'email', 'gstNo',
      'urlName', 'urlPlace', 'foodCGSTPercentage', 'foodSGSTPercentage', 'serviceCGSTPercentage', 'serviceSGSTPercentage',
      'serviceChargePercentage', 'lastKOTNo', 'lastSOTNo', 'lastMOTNo', 'lastFinalBillNo', 'lastFoodBillNo', 'lastServiceBillNo',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results[0];
    }

    return {};
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetHotel = async (employeeId, data) => {
  const FUNCTION_NAME = "redisSetHotel";

  try {
    // read single data
    await setOneHotelRedis(data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelHotel = async (employeeId, id) => {
  const FUNCTION_NAME = "redisDelHotel";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_HOTEL}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisHotels, redisSearchHotel, redisGetHotel, redisSetHotel, redisDelHotel };