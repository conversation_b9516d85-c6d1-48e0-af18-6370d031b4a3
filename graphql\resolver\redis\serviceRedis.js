const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneServiceRedis } = require("../../../helpers/redis/service");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "serviceRedis.js";

// get all documents of a collection
const redisServices = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisServices";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_SERVICE_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchService = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchService";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_SERVICE_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.unitPrice)) ||
          regex.test(item.description);

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetService = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetService";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_SERVICE_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetService = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetService";

  try {
    // read single data
    await setOneServiceRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelService = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelService";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_SERVICE}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisServices, redisSearchService, redisGetService, redisSetService, redisDelService };