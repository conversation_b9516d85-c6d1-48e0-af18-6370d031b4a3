const Role = require("../../../models/role");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "roleMongo.js";

// get all documents of a collection
const mongoRoles = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoRoles";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { name: 1 };
    const cursor = await Role.find(condition).sort(order);

    // spread all data
    const spread = cursor.map((object) => {
      // spread data
      let spreadObject = {};
      spreadObject = {
        ...object._doc,
        _id: object.id.toString(),
        hotel: object.hotel.toString(),
        permissions: object.permissions
      };

      // format permission array
      const permissionArray = spreadObject.permissions.map(({ module, operations }) => {
        const object = {
          module: module,
          operations: []
        };

        operations.forEach(operation => {
          object.operations.push(operation);
        });

        return object;
      });

      // spread data
      return {
        _id: spreadObject._id,
        hotel: spreadObject.hotel,
        name: spreadObject.name,
        color: spreadObject.color,
        permissions: permissionArray,
        description: spreadObject.description
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchRole = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchRole";
  const order = { name: 1 };

  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { hotel: hotelId, isEnable: true };
    }
    else {
      condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Role.find(condition).sort(order);

    // spread all data
    const spread = cursor.map((object) => {
      // spread data
      let spreadObject = {};
      spreadObject = {
        ...object._doc,
        _id: object.id.toString(),
        hotel: object.hotel.toString(),
        permissions: object.permissions
      };

      // format permission array
      const permissionArray = spreadObject.permissions.map(({ module, operations }) => {
        const object = {
          module: module,
          operations: []
        };

        operations.forEach(operation => {
          object.operations.push(operation);
        });

        return object;
      });

      // spread data
      return {
        _id: spreadObject._id,
        hotel: spreadObject.hotel,
        name: spreadObject.name,
        color: spreadObject.color,
        permissions: permissionArray,
        description: spreadObject.description
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const mongoGetRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetRole";

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Role.findOne(condition);

    // spread data
    let spreadObject = {};
    if (cursor) spreadObject = {
      ...cursor._doc,
      _id: cursor.id.toString(),
      hotel: cursor.hotel.toString(),
      permissions: cursor.permissions
    };

    // format permission array
    const permissionArray = spreadObject.permissions.map(({ module, operations }) => {
      const object = {
        module: module,
        operations: []
      };

      operations.forEach(operation => {
        object.operations.push(operation);
      });

      return object;
    });

    // spread data
    const spread = {
      _id: spreadObject._id,
      hotel: spreadObject.hotel,
      name: spreadObject.name,
      color: spreadObject.color,
      permissions: permissionArray,
      description: spreadObject.description
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModRole = async (hotelId, employeeId, id, name, color, permissions, description) => {
  const FUNCTION_NAME = "mongoModRole";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Role.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.ROLE_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = { hotel: hotelId, name: name, color: color, permissions: permissions, description: description };
    const modObject = await Role.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.ROLE_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { _id: modObject.id };
    const cursor = await Role.findOne(findCondition);

    // spread data
    let spreadObject = {};
    if (cursor) spreadObject = {
      ...cursor._doc,
      _id: cursor.id.toString(),
      hotel: cursor.hotel.toString(),
      permissions: cursor.permissions
    };

    // format permission array
    const permissionArray = spreadObject.permissions.map(({ module, operations }) => {
      const object = {
        module: module,
        operations: []
      };

      operations.forEach(operation => {
        object.operations.push(operation);
      });

      return object;
    });

    // spread data
    const spread = {
      _id: spreadObject._id,
      hotel: spreadObject.hotel,
      name: spreadObject.name,
      color: spreadObject.color,
      permissions: permissionArray,
      description: spreadObject.description,
      isEnable: spreadObject.isEnable,
      createdAt: spreadObject.createdAt,
      updatedAt: spreadObject.updatedAt
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoRoles, mongoSearchRole, mongoGetRole, mongoModRole };