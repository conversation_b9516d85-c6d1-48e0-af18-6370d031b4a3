const GST = require("../../models/gst");
const Hotel = require("../../models/hotel");
const Role = require("../../models/role");
const Agent = require("../../models/agent");
const Plan = require("../../models/plan");
const Identification = require("../../models/identification");
const PaymentMode = require("../../models/paymentMode");
const RoomCategory = require("../../models/roomCategory");
const Food = require("../../models/food");
const Service = require("../../models/service");
const Miscellaneous = require("../../models/miscellaneous");
const Employee = require("../../models/employee");
const Table = require("../../models/table");
const Room = require("../../models/room");
const { setAllGSTRedis } = require("../redis/gst");
const { getAllHotelRedis, setAllHotelRedis } = require("../redis/hotel");
const { setAllRoleRedis } = require("../redis/role");
const { setAllAgentRedis } = require("../redis/agent");
const { setAllPlanRedis } = require("../redis/plan");
const { setAllIdentificationRedis } = require("../redis/identification");
const { setAllPaymentModeRedis } = require("../redis/paymentMode");
const { setAllRoomCategoryRedis } = require("../redis/roomCategory");
const { setAllFoodRedis } = require("../redis/food");
const { setAllServiceRedis } = require("../redis/service");
const { setAllMiscellaneousRedis } = require("../redis/miscellaneous");
const { setAllEmployeeRedis } = require("../redis/employee");
const { setAllTableRedis } = require("../redis/table");
const { setAllRoomRedis } = require("../redis/room");
const { ERR_CODE } = require("../../configs/messageOptions");

const PopulateGST = async () => {
    try {
        const condition = {};
        const order = { minTariff: 1, maxTariff: 1 };
        const cursor = await GST.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id };
        });

        await setAllGSTRedis(data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("GST populated in redis");
    }
};

const PopulateHotel = async () => {
    try {
        const condition = { isEnable: true };
        const order = { name: 1 };
        const cursor = await Hotel.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id };
        });

        await setAllHotelRedis(data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Hotel populated in redis");
    }
};

const PopulateRole = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Role.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllRoleRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Role populated in redis");
    }
};

const PopulateEmployee = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Employee.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllEmployeeRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Employee populated in redis");
    }
};

const PopulateAgent = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Agent.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
        });

        await setAllAgentRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Agent populated in redis");
    }
};

const PopulatePlan = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Plan.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllPlanRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Plan populated in redis");
    }
};

const PopulateIdentification = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Identification.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllIdentificationRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Identification populated in redis");
    }
};

const PopulatePaymentMode = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await PaymentMode.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllPaymentModeRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Payment mode populated in redis");
    }
};

const PopulateRoomCategory = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await RoomCategory.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllRoomCategoryRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Room category populated in redis");
    }
};

const PopulateFood = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Food.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllFoodRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Food populated in redis");
    }
};

const PopulateService = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Service.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllServiceRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Service populated in redis");
    }
};

const PopulateMiscellaneous = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { name: 1 };
        const cursor = await Miscellaneous.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllMiscellaneousRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Miscellaneous populated in redis");
    }
};

const PopulateTable = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const order = { no: 1 };
        const cursor = await Table.find(condition).sort(order);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllTableRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Table populated in redis");
    }
};

const PopulateRoom = async (hotelId) => {
    try {
        const condition = { hotel: hotelId, isEnable: true };
        const cursor = await Room.find(condition);
        const data = cursor.map((object) => {
            return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
        });

        await setAllRoomRedis(hotelId, data);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
    finally {
        console.log("Room populated in redis");
    }
};

const PopulateRedis = async () => {
    try {

        await PopulateGST();
        await PopulateHotel();

        const objectArray = await getAllHotelRedis();
        objectArray.map(async (object) => {
            await PopulateRole(object._id);
            await PopulateEmployee(object._id);
            await PopulateAgent(object._id);
            await PopulatePlan(object._id);
            await PopulateIdentification(object._id);
            await PopulatePaymentMode(object._id);
            await PopulateRoomCategory(object._id);
            await PopulateFood(object._id);
            await PopulateService(object._id);
            await PopulateMiscellaneous(object._id);
            await PopulateTable(object._id);
            await PopulateRoom(object._id);
        });
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = PopulateRedis;