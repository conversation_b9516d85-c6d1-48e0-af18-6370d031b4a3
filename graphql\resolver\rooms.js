const { mongoRooms, mongoSearchRoom, mongoGetRoom, mongoAddRoom, mongoModRoom, mongoDelRoom, mongoDelRooms } = require("./mongo/roomMongo");
const { redisRooms, redisSearchRoom, redisGetRoom, redisSetRoom, redisDelRoom } = require("./redis/roomRedis");
const { roomDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "rooms.js";

// get all documents of a collection
const rooms = async (args, req) => {
  const FUNCTION_NAME = "rooms";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObject = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObject = await mongoRooms(hotelId, employeeId);
    }
    else {
      allObject = await redisRooms(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_LIST);

    // return output
    return allObject.map(async (object) => {
      return await roomDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchRoom = async (args, req) => {
  const FUNCTION_NAME = "searchRoom";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.roomSearchInput ? args.roomSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchRoom(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchRoom(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await roomDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getRoom = async (args, req) => {
  const FUNCTION_NAME = "getRoom";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetRoom(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetRoom(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.ROOM_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_GET);

    // return output
    return await roomDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addRoom = async (args, req) => {
  const FUNCTION_NAME = "addRoom";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { category, no, accommodation, tariff, extraBedTariff, extraPersonTariff, maxDiscount } = args.roomInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (category === "") throw new customError(ERR_MSG.INVALID_CATEGORY, ERR_CODE.BAD_REQUEST);
    if (no === "") throw new customError(ERR_MSG.INVALID_NO, ERR_CODE.BAD_REQUEST);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (tariff === "") throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (tariff <= 0) throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);

    const _category = category.trim().toUpperCase();
    const _no = no.trim().toUpperCase();
    const _accommodation = accommodation;
    const _tariff = tariff;
    const _maxDiscount = maxDiscount ? maxDiscount : 0;
    const _extraBedTariff = extraBedTariff ? extraBedTariff : 0;
    const _extraPersonTariff = extraPersonTariff ? extraPersonTariff : 0;

    // add data
    const addObject = await mongoAddRoom(
      hotelId,
      employeeId,
      _category,
      _no,
      _accommodation,
      _tariff,
      _extraBedTariff,
      _extraPersonTariff,
      _maxDiscount);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRoom(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_ADD);

    // return output
    return await roomDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modRoom = async (args, req) => {
  const FUNCTION_NAME = "modRoom";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, category, no, accommodation, tariff, extraBedTariff, extraPersonTariff, maxDiscount } = args.roomInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (category === "") throw new customError(ERR_MSG.INVALID_CATEGORY, ERR_CODE.BAD_REQUEST);
    if (no === "") throw new customError(ERR_MSG.INVALID_NO, ERR_CODE.BAD_REQUEST);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (tariff === "") throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (tariff <= 0) throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);

    const _category = category.trim().toUpperCase();
    const _no = no.trim().toUpperCase();
    const _accommodation = accommodation;
    const _tariff = tariff;
    const _maxDiscount = maxDiscount ? maxDiscount : 0;
    const _extraBedTariff = extraBedTariff ? extraBedTariff : 0;
    const _extraPersonTariff = extraPersonTariff ? extraPersonTariff : 0;

    // modify data
    const modObject = await mongoModRoom(
      hotelId,
      employeeId,
      _id,
      _category,
      _no,
      _accommodation,
      _tariff,
      _extraBedTariff,
      _extraPersonTariff,
      _maxDiscount);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRoom(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_MOD);

    // return output
    return await roomDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delRoom = async (args, req) => {
  const FUNCTION_NAME = "delRoom";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelRoom(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelRoom(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_DEL);

    // return output
    return await roomDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delRooms = async (args, req) => {
  const FUNCTION_NAME = "delRooms";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.roomsInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const objectObjects = await mongoDelRooms(hotelId, employeeId, _ids);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      objectObjects.map(async (object) => {
        await redisDelRoom(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOMS_DEL);

    // return output
    return objectObjects.map(async (object) => {
      return await roomDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { rooms, searchRoom, getRoom, addRoom, modRoom, delRoom, delRooms };