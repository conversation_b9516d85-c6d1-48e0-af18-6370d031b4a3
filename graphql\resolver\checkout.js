const Hotel = require("../../models/hotel");
const Room = require("../../models/room");
const { mongoAddCheckout } = require("./mongo/checkoutMongo");
const { setOneHotelRedis } = require("../../helpers/redis/hotel");
const { setAllRoomRedis } = require("../../helpers/redis/room");
const { checkoutDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "checkout.js";

// insert a document into the collection
const addCheckout = async (args, req) => {
  const FUNCTION_NAME = "addCheckout";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // add checkout
    const addObject = await mongoAddCheckout(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelId, hotelSpread);

      // update rooms
      const roomCondition = { hotel: hotelId, isEnable: true };
      const roomOrder = { category: 1, no: 1 };
      const roomCursor = await Room.find(roomCondition).sort(roomOrder);
      const roomSpread = roomCursor.map((object) => {
        return {
          ...object._doc,
          _id: object.id,
          hotel: object.hotel.toString()
        };
      });
      await setAllRoomRedis(hotelId, roomSpread);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.CHECK_OUT);

    // return output
    return await checkoutDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { addCheckout };