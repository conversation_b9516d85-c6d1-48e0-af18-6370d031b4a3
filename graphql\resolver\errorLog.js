const fs = require('fs');
const path = require('path');
const moment = require('moment');
const readline = require('readline');
const { LogFileDetail, ErrorLogDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");

// Convert bytes to kilobytes
const bytesToKB = (bytes) => {
  const megabytes = bytes / 1024; // 1 KB = 1024 bytes
  return megabytes;
};

function createFolder(folderPath) {
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true });
  }
}

// Get all documents of a collection
const errorLogFiles = async (args, req) => {
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  // check for authentication and authorization
  if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
  if (!await isAuthorized(employeeRole, MODEL_LIST.ERROR_LOG, OPERATION_LIST.VIEW))
    throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

  const logFolder = path.join(".", process.env.APP_LOG_FOLDER);
  const errorFolder = path.join(logFolder, process.env.APP_ERROR_FOLDER);

  createFolder(errorFolder);

  return new Promise((resolve, reject) => {
    fs.readdir(errorFolder, async (error, files) => {
      if (error) return reject(error);

      const logFiles = files.filter(name => path.extname(name) === '.log');
      const logFileDetails = await Promise.all(
        logFiles.map(name => new Promise((res, rej) => {
          const fullPath = path.join(errorFolder, name);

          fs.stat(fullPath, (error, stats) => {
            if (error) return rej(error);

            const sizeInBytes = stats.size;
            const sizeInKB = bytesToKB(sizeInBytes);
            const dateDDMMYYYY = moment(stats.mtime).format('DD/MM/YYYY');

            res({
              fileName: name,
              fileSize: sizeInKB.toFixed(2) + ' KB', // Display size in KB with 2 decimal places
              fileDate: dateDDMMYYYY
            });
          });
        }))
      );

      const sortedFiles = logFileDetails.sort((a, b) => {
        const dateA = new Date(a.fileDate.split("/").reverse().join("-"));
        const dateB = new Date(b.fileDate.split("/").reverse().join("-"));
        return dateB - dateA;
      });

      resolve(sortedFiles);
    });
  }).catch((error) => {
    throw new customError(error.message, error.code);
  });
};

const errorLogDetail = async (args, req) => {
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { fileName } = args.logInput;

  // check for authentication and authorization
  if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
  if (!await isAuthorized(employeeRole, MODEL_LIST.ERROR_LOG, OPERATION_LIST.VIEW))
    throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

  const logFolder = path.join(".", process.env.APP_LOG_FOLDER);
  const errorFolder = path.join(logFolder, process.env.APP_ERROR_FOLDER);
  const objectArray = [];
  const logFilePath = path.join(errorFolder, fileName);

  return new Promise((resolve, reject) => {
    // Create a readline interface
    const rl = readline.createInterface({
      input: fs.createReadStream(logFilePath),
      output: process.stdout,
      terminal: false
    });

    // Read the file line by line
    rl.on('line', (line) => {
      try {
        const jsonLine = JSON.parse(line);
        objectArray.push(jsonLine);
      } catch (error) {
        reject(new customError('Error parsing JSON line: ' + error.message, error.code));
      }
    });

    // When the file reading is complete
    rl.on('close', () => {
      resolve(objectArray);
    });

    rl.on('error', (error) => {
      reject(new customError('Error reading file: ' + error.message, error.code));
    });
  })
    .then(async (data) => {
      const sortedActivity = data.sort((a, b) => {
        const dateA = new Date(a.timestamp.split("/").reverse().join("-"));
        const dateB = new Date(b.timestamp.split("/").reverse().join("-"));
        return dateB - dateA;
      });

      const results = await Promise.all(sortedActivity.map(async (object) => {
        return await ErrorLogDetail(object);
      }));

      return results;
    })
    .catch((error) => {
      throw new customError(error.message, error.code);
    });
};

const delErrorLogFiles = async (args, req) => {
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { fileNames } = args.logsInput;

  // check for authentication and authorization
  if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
  if (!await isAuthorized(employeeRole, MODEL_LIST.ERROR_LOG, OPERATION_LIST.REMOVE))
    throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

  const logFolder = path.join(".", process.env.APP_LOG_FOLDER);
  const errorFolder = path.join(logFolder, process.env.APP_ERROR_FOLDER);
  const logFileArray = [];

  const deleteFile = (errorFolder) => {
    return new Promise((resolve, reject) => {
      fs.access(errorFolder, fs.constants.F_OK, (error) => {
        if (error) return reject(new customError(`File does not exist: ${errorFolder}`, error.code));

        fs.unlink(errorFolder, (error) => {
          if (error) return reject(new customError(`Error deleting file: ${errorFolder}`, error.code));
          resolve();
        });
      });
    });
  };

  try {
    const deletionPromises = fileNames.map(name => {
      const fullPath = path.join(errorFolder, name);

      fs.stat(fullPath, (error, stats) => {
        if (error) {
          return error;
        }

        logFileArray.push({
          name: name,
          size: stats.size,
          date: stats.mtime
        });
      });

      return deleteFile(fullPath);
    });

    await Promise.all(deletionPromises);
    const logFileData = logFileArray.map(object => LogFileDetail(object));
    return logFileData;
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

module.exports = { errorLogFiles, errorLogDetail, delErrorLogFiles };