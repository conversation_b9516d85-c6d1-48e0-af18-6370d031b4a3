const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const Employee = require("../../models/employee");
const Role = require("../../models/role");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { generateOTP } = require("../../helpers/otp");
const sendOtpEmail = require("../../helpers/email");
// const sendOtpSMS = require("../../helpers/sms");
const { customError } = require('../../helpers/customError');
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "auth.js";

const checkAuth = async (args, req) => {
  const FUNCTION_NAME = "checkAuth";
  const { hotelId, employeeId, isAuthenticated } = req;

  let status = false;

  try {
    if (isAuthenticated) status = true;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    status = false;
  }

  // record activity in log
  writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.CHECK_AUTH);

  // return output
  return { status };
};

const findEmployee = async (args) => {
  let search = args.employeeSearchInput;
  let employee = null;

  try {
    employee = await Employee.find({
      $or: [
        { mobile: { $regex: ".*" + search + ".*" } },
        { email: { $regex: ".*" + search + ".*" } }
      ],
      isEnable: true,
    });

    return {
      mobile: employee.mobile,
      email: employee.email
    };
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

const forgetPassword = async (args) => {
  let userName = args.forgetPasswordInput;
  let employee = null;

  try {
    if (!userName)
      throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.BAD_REQUEST);

    if (parseInt(userName)) {
      employee = await Employee.findOne({ mobile: userName, isEnable: true });
    } else {
      employee = await Employee.findOne({ email: userName, isEnable: true });
    }

    if (!employee)
      throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    //generate otp
    const genOtp = generateOTP();
    const newOtp = await bcrypt.hash(genOtp, 12);

    employee.otp = newOtp;
    employee.expirationTime = null;
    employee.iat = null;

    await employee.save();

    //send by the email
    const emailStatus = await sendOtpEmail(employee.email, genOtp);
    if (!emailStatus) throw new customError(JSON.stringify({ status: ERR_CODE.BAD_REQUEST, message: ERR_MSG.OTP }));

    //send by the sms 
    // const smsStatus = await sendOtpSMS(employee.mobile, genOtp);
    // if (!smsStatus) throw new customError(JSON.stringify({ status: ERR_CODE.BAD_REQUEST, message: ERR_MSG.OTP }));

    return {
      mobile: employee.mobile,
      email: employee.email
    };

  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

const login = async (args, req) => {
  const { userName, password, hotelId } = args.loginInput;
  let employee = null;

  try {
    if (!userName) throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.BAD_REQUEST);
    if (!password) throw new customError(ERR_MSG.INVALID_PASSWORD, ERR_CODE.BAD_REQUEST);

    if (hotelId) {
      if (parseInt(userName)) employee = await Employee.findOne({ hotel: hotelId, mobile: userName, isEnable: true });
      else employee = await Employee.findOne({ hotelId: hotelId, email: userName, isEnable: true });
    }
    else {
      if (parseInt(userName)) employee = await Employee.findOne({ mobile: userName, isEnable: true });
      else employee = await Employee.findOne({ email: userName, isEnable: true });
    }

    if (!employee) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const role = await Role.findOne({ _id: employee.role, isEnable: true });
    if (!role) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const matchEmployee = await bcrypt.compare(password, employee.password);
    if (!matchEmployee) throw new customError(ERR_MSG.INVALID_PASSWORD, ERR_CODE.BAD_REQUEST);

    const accessToken = jwt.sign(
      {
        UserInfo: {
          hotelId: employee.hotel,
          employeeId: employee._id,
          employeeName: employee.name,
          employeeRoleColor: role.color,
          employeeRole: role.name
        }
      },
      process.env.JWT_TOKEN_SECRET,
      { expiresIn: process.env.JWT_TOKEN_EXPIRES }
    );

    const refreshToken = jwt.sign(
      {
        UserInfo: {
          hotelId: employee.hotel,
          employeeId: employee._id,
          employeeName: employee.name,
          employeeRoleColor: role.color,
          employeeRole: role.name
        }
      },
      process.env.JWT_REFRESH_TOKEN_SECRET,
      { expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES }
    );

    jwt.verify(
      accessToken,
      process.env.JWT_TOKEN_SECRET,
      async (error, decoded) => {
        if (error) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);

        employee.otp = "";
        employee.expirationTime = null;
        employee.iat = decoded.iat;

        await employee.save();
      }
    );

    req.res.cookie('accessToken', accessToken, { httpOnly: true, sameSite: 'strict', secure: true, maxAge: 15 * 60 * 1000 });
    req.res.cookie('refreshToken', refreshToken, { httpOnly: true, sameSite: 'strict', secure: true, maxAge: 7 * 24 * 60 * 60 * 1000 });

    return { accessToken, refreshToken };
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

const loginOtp = async (args, req) => {
  const FUNCTION_NAME = "loginOtp";
  const { userName, otp, hotelId } = args.loginOtpInput;

  let employee = null;

  try {
    if (!userName)
      throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.BAD_REQUEST);
    if (!otp)
      throw new customError(ERR_MSG.INVALID_OTP, ERR_CODE.BAD_REQUEST);

    if (hotelId) {
      if (parseInt(userName)) employee = await Employee.findOne({ hotel: hotelId, mobile: userName, isEnable: true });
      else employee = await Employee.findOne({ hotelId: hotelId, email: userName, isEnable: true });
    }
    else {
      if (parseInt(userName)) employee = await Employee.findOne({ mobile: userName, isEnable: true });
      else employee = await Employee.findOne({ email: userName, isEnable: true });
    }

    if (!employee) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const role = await Role.findOne({ _id: employee.role, isEnable: true });
    if (!role) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const matchEmployee = await bcrypt.compare(otp, employee.otp);
    if (!matchEmployee)
      throw new customError(ERR_MSG.INVALID_PASSWORD, ERR_CODE.FORBIDDEN);

    const accessToken = jwt.sign(
      {
        UserInfo: {
          hotelId: employee.hotel,
          employeeId: employee._id,
          employeeName: employee.name,
          employeeRoleColor: role.color,
          employeeRole: role.name
        },
      },
      process.env.JWT_TOKEN_SECRET,
      { expiresIn: process.env.JWT_TOKEN_EXPIRES }
    );

    const refreshToken = jwt.sign(
      {
        UserInfo: {
          hotelId: employee.hotel,
          employeeId: employee._id,
          employeeName: employee.name,
          employeeRoleColor: role.color,
          employeeRole: role.name
        }
      },
      process.env.JWT_REFRESH_TOKEN_SECRET,
      { expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES }
    );

    jwt.verify(
      accessToken,
      process.env.JWT_TOKEN_SECRET,
      async (error, decoded) => {
        if (error) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);

        employee.password = employee.otp;
        employee.otp = "";
        employee.expirationTime = null;
        employee.iat = decoded.iat;

        await employee.save();
      }
    );

    req.res.cookie('accessToken', accessToken, { httpOnly: true, sameSite: 'strict', secure: true, maxAge: 15 * 60 * 1000 });
    req.res.cookie('refreshToken', refreshToken, { httpOnly: true, sameSite: 'strict', secure: true, maxAge: 7 * 24 * 60 * 60 * 1000 });

    return { accessToken, refreshToken };
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

const changePassword = async (args, req) => {
  const FUNCTION_NAME = "changePassword";
  const { hotelId, employeeId } = req;

  try {
    const { _id, oldPassword, newPassword } = args.changePasswordInput;

    if (!_id) throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.BAD_REQUEST);
    if (!oldPassword) throw new customError(ERR_MSG.INVALID_OLD_PASSWORD, ERR_CODE.BAD_REQUEST);
    if (!newPassword) throw new customError(ERR_MSG.INVALID_PASSWORD, ERR_CODE.BAD_REQUEST);

    const employee = await Employee.findOne({ _id: _id, isEnable: true });

    const matchEmployee = await bcrypt.compare(oldPassword, employee.password);
    if (!matchEmployee) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.FORBIDDEN);

    const newPass = await bcrypt.hash(newPassword, 12);
    const dbMod = await Employee.findByIdAndUpdate(employee._id, {
      password: newPass,
      otp: null,
      expiration_time: null
    });

    if (!dbMod) throw new customError(ERR_MSG.PASSWORD_NOT_SAVE, ERR_CODE.INTERNAL);

    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.CHANGE_PASSWORD);
    return {
      mobile: employee.mobile,
      email: employee.email
    };
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

const logout = async (args, req) => {
  const _id = args.logoutInput;

  let employee = null;

  try {
    if (!_id)
      throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.BAD_REQUEST);

    employee = await Employee.findOne({ _id: _id, isEnable: true });

    if (!employee)
      throw new customError(ERR_MSG.E, ERR_CODE.FORBIDDEN, ERR_CODE.FORBIDDEN);

    employee.iat = null;
    await employee.save();

    req.res.clearCookie('accessToken');
    req.res.clearCookie('refreshToken');

    return {
      mobile: employee.mobile,
      email: employee.email
    };
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  findEmployee, forgetPassword, login, loginOtp, changePassword, logout, checkAuth
};