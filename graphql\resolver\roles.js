const { mongoRoles, mongoSearchRole, mongoGetRole, mongoAddRole, mongoModRole, mongoDelRole, mongoDelRoles } = require("./mongo/roleMongo");
const { redisRoles, redisSearchRole, redisGetRole, redisSetRole, redisDelRole } = require("./redis/roleRedis");
const { roleDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST, ALL_MODEL_WISE_PERMISSION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "roles.js";

// get all documents of a collection
const roles = async (args, req) => {
  const FUNCTION_NAME = "roles";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let cursor = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PLAN, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      cursor = await mongoRoles(hotelId, employeeId);
    }
    else {
      cursor = await redisRoles(hotelId, employeeId);
    }

    // spread data
    const spread = cursor.map((object) => {
      const permissionArray = ALL_MODEL_WISE_PERMISSION_LIST.map(({ module, operations }) => {
        const itemPermissionObject = {
          module: module,
          operations: []
        };

        itemPermissionObject.operations = operations.map((item) => {
          return {
            operation: item,
            access: false
          };
        });

        const foundPermission = object.permissions.find((item) => {
          if (item.module === module) {
            return item.operations;
          } else {
            return null;
          }
        });

        if (foundPermission) {
          operations.map((operation) => {
            const fountIdx = foundPermission.operations.indexOf(operation.operation);
            if (fountIdx >= 0) {
              itemPermissionObject.operations[fountIdx].access = true;
            }
          });
        }

        return itemPermissionObject;
      })

      return {
        _id: object._id,
        hotel: object.hotel,
        name: object.name,
        color: object.color,
        permissions: permissionArray,
        description: object.description
      };

    });

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_LIST);

    // return output
    return spread.map(async (object) => {
      return await roleDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchRole = async (args, req) => {
  const FUNCTION_NAME = "searchRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.roleSearchInput ? args.roleSearchInput.trim().toUpperCase() : "";

  let cursor = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROLE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      cursor = await mongoSearchRole(hotelId, employeeId, searchKey);
    }
    else {
      cursor = await redisSearchRole(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_SEARCH);

    // spread data
    const spread = cursor.map((object) => {
      const permissionArray = ALL_MODEL_WISE_PERMISSION_LIST.map(({ module, operations }) => {
        const itemPermissionObject = {
          module: module,
          operations: []
        };

        itemPermissionObject.operations = operations.map((item) => {
          return {
            operation: item,
            access: false
          };
        });

        const foundPermission = object.permissions.find((item) => {
          if (item.module === module) {
            return item.operations;
          } else {
            return null;
          }
        });

        if (foundPermission) {
          operations.map((operation) => {
            const fountIdx = foundPermission.operations.indexOf(operation.operation);
            if (fountIdx >= 0) {
              itemPermissionObject.operations[fountIdx].access = true;
            }
          });
        }

        return itemPermissionObject;
      })

      return {
        _id: object._id,
        hotel: object.hotel,
        name: object.name,
        color: object.color,
        permissions: permissionArray,
        description: object.description
      };

    });

    // return output
    return spread.map(async (object) => {
      return await roleDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getRole = async (args, req) => {
  const FUNCTION_NAME = "getRole";
  const { hotelId, employeeId } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetRole(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetRole(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_GET);

    const permissionArray = ALL_MODEL_WISE_PERMISSION_LIST.map(({ module, operations }) => {
      const itemPermissionObject = {
        module: module,
        operations: []
      };

      itemPermissionObject.operations = operations.map((item) => {
        return {
          operation: item,
          access: false
        };
      });

      const foundPermission = getObject.permissions.find((item) => {
        if (item.module === module) {
          return item.operations;
        } else {
          return null;
        }
      });

      if (foundPermission) {
        operations.map((operation) => {
          const fountIdx = foundPermission.operations.indexOf(operation.operation);
          if (fountIdx >= 0) {
            itemPermissionObject.operations[fountIdx].access = true;
          }
        });
      }

      return itemPermissionObject;
    })

    const object = {
      _id: getObject._id,
      hotel: getObject.hotel,
      name: getObject.name,
      color: getObject.color,
      permissions: permissionArray,
      description: getObject.description
    };

    // return output
    return await roleDetail(object);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modRole = async (args, req) => {
  const FUNCTION_NAME = "modRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, color, permissions, description } = args.roleInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROLE, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (!color) throw new customError(ERR_MSG.INVALID_COLOR, ERR_CODE.CONFLICT);

    const _name = name ? name.trim().toUpperCase() : findObject.name;
    const _color = color ? color.trim().toLowerCase() : color;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModRole(hotelId, employeeId, _id, _name, _color, permissions, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRole(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_MOD);

    const permissionArray = ALL_MODEL_WISE_PERMISSION_LIST.map(({ module, operations }) => {
      const itemPermissionObject = {
        module: module,
        operations: []
      };

      itemPermissionObject.operations = operations.map((item) => {
        return {
          operation: item,
          access: false
        };
      });

      const foundPermission = modObject.permissions.find((item) => {
        if (item.module === module) {
          return item.operations;
        } else {
          return null;
        }
      });

      if (foundPermission) {
        operations.map((operation) => {
          const fountIdx = foundPermission.operations.indexOf(operation.operation);
          if (fountIdx >= 0) {
            itemPermissionObject.operations[fountIdx].access = true;
          }
        });
      }

      return itemPermissionObject;
    })

    const object = {
      _id: modObject._id,
      hotel: modObject.hotel,
      name: modObject.name,
      color: modObject.color,
      permissions: permissionArray,
      description: modObject.description
    };

    // return output
    return await roleDetail(object);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { roles, searchRole, getRole, modRole };