const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;
const { TABLE_STATUS } = require("../configs/tableOptions");

const itemSchema = new Schema(
  {
    item: {
      type: Schema.Types.ObjectId,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    foodCGSTPercentage: {
      type: Number
    },
    foodSGSTPercentage: {
      type: Number
    },
    serviceCGSTPercentage: {
      type: Number
    },
    serviceSGSTPercentage: {
      type: Number
    },
    serviceChargePercentage: {
      type: Number
    },
    foodCGSTAmount: {
      type: Number
    },
    foodSGSTAmount: {
      type: Number
    },
    serviceCGSTAmount: {
      type: Number
    },
    serviceSGSTAmount: {
      type: Number
    },
    serviceChargeAmount: {
      type: Number
    },
    status: {
      type: String,
      default: TABLE_STATUS.ORDERED
    }
  }
);

const tokenSchema = new Schema(
  {
    tokenNo: {
      type: Number,
      required: true
    },
    employee: {
      type: Schema.Types.ObjectId
    },
    deliveryDate: {
      type: String
    },
    deliveryTime: {
      type: String
    },
    items: [itemSchema],
    status: {
      type: String,
      default: TABLE_STATUS.ORDERED
    }
  },
  { timestamps: true }
);

const orderSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    employees: [
      {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "Employee"
      }
    ],
    booking: {
      type: Schema.Types.ObjectId,
      ref: "Booking"
    },
    tables: [
      {
        type: Schema.Types.ObjectId,
        ref: "Table"
      }
    ],
    type: {
      type: String,
      required: true
    },
    tokens: [tokenSchema],
    status: {
      type: String,
      default: TABLE_STATUS.ORDERED
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Order", orderSchema);