const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");

// Function to get one room data from Redis
const getOneRoomRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_ROOM}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all room data from Redis
const getAllRoomRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROOM}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByNo = parsedData.sort((a, b) => a.no.localeCompare(b.no));

        return sortedByNo;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set room data in Redis
const setOneRoomRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneRoomRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_ROOM}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    category: data.category,
                    no: data.no,
                    accommodation: data.accommodation,
                    tariff: data.tariff,
                    extraBedTariff: data.extraBedTariff,
                    extraPersonTariff: data.extraPersonTariff,
                    maxDiscount: data.maxDiscount,
                    status: data.status,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set room data in Redis
const setAllRoomRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllRoomRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_ROOM}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        category: data.category,
                        no: data.no,
                        accommodation: data.accommodation,
                        tariff: data.tariff,
                        extraBedTariff: data.extraBedTariff,
                        extraPersonTariff: data.extraPersonTariff,
                        maxDiscount: data.maxDiscount,
                        status: data.status,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete room data from Redis
const delOneRoomRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_ROOM}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete room data from Redis
const delAllRoomRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROOM}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = {
    getOneRoomRedis,
    getAllRoomRedis,
    setOneRoomRedis,
    setAllRoomRedis,
    delOneRoomRedis,
    delAllRoomRedis
};
