const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneFoodRedis } = require("../../../helpers/redis/food");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "foodRedis.js";

// get all documents of a collection
const redisFoods = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisFoods";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_FOOD_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchFood = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchFood";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_FOOD_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.unitPrice)) ||
          regex.test(item.description);

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetFood = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetFood";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_FOOD_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetFood = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetFood";

  try {
    // read single data
    await setOneFoodRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelFood = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelFood";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_FOOD}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisFoods, redisSearchFood, redisGetFood, redisSetFood, redisDelFood };