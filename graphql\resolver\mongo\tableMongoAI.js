const mongoose = require("mongoose");
const Table = require("../../../models/table");
const { processTextToEmbedding } = require("../../../helpers/embedding");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "tableMongoAI.js";

// search within collection
const mongoAISearchTable = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoAISearchTable";

  try {
    if (searchKey === "") {
      const condition = { hotel: hotelId, isEnable: true };
      const order = { no: 1 };
      const cursor = await Table.find(condition).sort(order);

      // spread data
      const spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
      });

      return spread;
    }
    else {
      // get data
      // const searchEmbedding = await createEmbedding(searchKey);
      const searchEmbedding = await processTextToEmbedding(searchKey);

      const pipeline = [
        {
          $vectorSearch: {
            index: process.env.DB_TABLE_INDEX_VECTOR,
            path: 'embedding',
            queryVector: searchEmbedding,
            numCandidates: 10,
            limit: 10,
            filter: {
              $and: [
                { hotel: { $eq: new mongoose.Types.ObjectId(hotelId) } },
                { isEnable: { $eq: true } }
              ]
            }
          }
        },
        {
          $project: {
            _id: 1,
            hotel: 1,
            no: 1,
            accommodation: 1,
            description: 1,
            score: { $meta: 'vectorSearchScore' }
          }
        }
      ];
      const cursor = await Table.aggregate(pipeline).exec();

      // spread data
      const spread = cursor
        .filter((object) => object.score >= 0.65)
        .map((object) => {
          return {
            _id: object._id.toString(),
            hotel: object.hotel.toString(),
            no: object.no,
            accommodation: object.accommodation,
            description: object.description
          };
        });

      return spread;
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAIAddTable = async (hotelId, employeeId, no, accommodation, description) => {
  const FUNCTION_NAME = "mongoAIAddTable";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, no: no, isEnable: true };
    const duplicateCursor = await Table.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.TABLE_CONFLICT, ERR_CODE.CONFLICT);

    // add data in vector db
    const summary = `Table '${no}' can accommodate maximum ${accommodation} numbers of guests. ${description ? ` ${description}.` : ''}`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // insert data in db
    const data = {
      hotel: hotelId,
      no: no,
      accommodation: accommodation,
      description: description,
      embedding: embedding
    };
    const addData = new Table(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.TABLE_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Table.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoAIModTable = async (hotelId, employeeId, id, no, accommodation, description) => {
  const FUNCTION_NAME = "mongoAIModTable";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, no: no, isEnable: true };
    const duplicateCursor = await Table.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.TABLE_CONFLICT, ERR_CODE.CONFLICT);

    // add data in vector db
    const summary = `Table '${no}' can accommodate maximum ${accommodation} numbers of guests. ${description ? ` ${description}.` : ''}`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // change data in db
    const modData = {
      no: no,
      accommodation: accommodation,
      description: description,
      embedding: embedding
    };
    const modObject = await Table.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.TABLE_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Table.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoAISearchTable, mongoAIAddTable, mongoAIModTable };