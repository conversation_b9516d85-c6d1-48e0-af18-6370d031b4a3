const Hotel = require("../../../models/hotel");
const Table = require("../../../models/table");
const Order = require("../../../models/order");
const Booking = require("../../../models/booking");
const Bill = require("../../../models/bill");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ROOM_STATUS } = require("../../../configs/roomOptions");
const { TABLE_STATUS } = require("../../../configs/tableOptions");
const { ORDER_TYPE } = require("../../../configs/orderOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "billMongo.js";

// add a document of a collection
const addBill = async (hotelId, employeeId, orderId, isAttach = true) => {
  const FUNCTION_NAME = "addBill";

  let addBill = null;
  let billNo = 0;
  let foodCGSTAmount = 0;
  let foodSGSTAmount = 0;
  let serviceCGSTAmount = 0;
  let serviceSGSTAmount = 0;
  let serviceChargeAmount = 0;
  let billAmount = 0;
  let totalAmount = 0;

  try {
    // find bill
    const billCondition = { hotel: hotelId, order: orderId };
    const billCursor = await Bill.findOne(billCondition);
    if (billCursor) throw new customError(ERR_MSG.BILL_CONFLICT, ERR_CODE.NOT_EXISTS);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);

    let foodBillNo = Number(parseInt(hotelCursor.lastFoodBillNo));
    let serviceBillNo = Number(parseInt(hotelCursor.lastServiceBillNo));
    let miscellaneousBillNo = Number(parseInt(hotelCursor.lastMiscellaneousBillNo));
    const foodCGSTPercentage = Number(parseFloat(hotelCursor.foodCGSTPercentage).toFixed(2));
    const foodSGSTPercentage = Number(parseFloat(hotelCursor.foodSGSTPercentage).toFixed(2));
    const serviceCGSTPercentage = Number(parseFloat(hotelCursor.serviceCGSTPercentage).toFixed(2));
    const serviceSGSTPercentage = Number(parseFloat(hotelCursor.serviceSGSTPercentage).toFixed(2));
    const serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));

    // find order
    const orderCondition = { _id: orderId, hotel: hotelId };
    const orderCursor = await Order.findOne(orderCondition);
    if (!orderCursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    orderCursor.tokens.map((token) => {
      token.items.map((item) => {
        totalAmount = (Number(parseFloat(totalAmount).toFixed(2)) + Number(parseFloat(item.unitPrice).toFixed(2))) *
          Number(parseInt(item.quantity));
      });
    });

    switch (orderCursor.type) {
      case ORDER_TYPE.FOOD:
        foodBillNo = foodBillNo + 1;
        billNo = foodBillNo;
        foodCGSTAmount = (Number(parseFloat(foodCGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        foodSGSTAmount = (Number(parseFloat(foodSGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        billAmount = Number(parseFloat(totalAmount).toFixed(2)) +
          Number(parseFloat(foodCGSTAmount).toFixed(2)) +
          Number(parseFloat(foodSGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceChargeAmount).toFixed(2));

        // insert bill data to bill document
        addBill = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          cGSTPercentage: Number(parseFloat(foodCGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(foodCGSTAmount).toFixed(2)),
          sGSTPercentage: Number(parseFloat(foodSGSTPercentage).toFixed(2)),
          sGSTAmount: Number(parseFloat(foodSGSTAmount).toFixed(2)),
          serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
          serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
          billAmount: Number(parseFloat(billAmount).toFixed(2)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      case ORDER_TYPE.SERVICE:
        serviceBillNo = serviceBillNo + 1;
        billNo = serviceBillNo;
        serviceCGSTAmount = (Number(parseFloat(serviceCGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceSGSTAmount = (Number(parseFloat(serviceSGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        billAmount = Number(parseFloat(totalAmount).toFixed(2)) +
          Number(parseFloat(serviceCGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceSGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceChargeAmount).toFixed(2));

        // insert bill data to bill document
        addBill = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          cGSTPercentage: Number(parseFloat(serviceCGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(serviceCGSTAmount).toFixed(2)),
          sGSTPercentage: Number(parseFloat(serviceSGSTPercentage).toFixed(2)),
          sGSTAmount: Number(parseFloat(serviceSGSTAmount).toFixed(2)),
          serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
          serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
          billAmount: Number(parseFloat(billAmount).toFixed(2)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      case ORDER_TYPE.MISCELLANEOUS:
        miscellaneousBillNo = miscellaneousBillNo + 1;
        billNo = miscellaneousBillNo;
        billAmount = Number(parseFloat(totalAmount).toFixed(2));

        // insert bill data to bill document
        addBill = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          cGSTPercentage: 0,
          cGSTAmount: 0,
          sGSTPercentage: 0,
          sGSTAmount: 0,
          serviceChargePercentage: 0,
          serviceChargeAmount: 0,
          billAmount: Number(parseFloat(billAmount).toFixed(2)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      default:
        break;
    }

    // update last food bill no in hotel document
    await Hotel.findByIdAndUpdate(hotelId, {
      lastFoodBillNo: foodBillNo,
      lastServiceBillNo: serviceBillNo,
      lastMiscellaneousBillNo: miscellaneousBillNo
    });

    // add bill
    const addObject = await addBill.save();
    if (!addObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    // update order
    const updateOrder = await Order.findByIdAndUpdate(orderId, {
      status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
    });
    if (!updateOrder) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    if (isAttach) {
      // find booking
      const bookingCondition = { _id: orderCursor.booking, hotel: hotelId };
      const bookingCursor = await Booking.findOne(bookingCondition);
      if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

      const 
      // update booking
      const updateBooking = await Booking.updateMany(
        { hotel: hotelId, _id: orderCursor.booking },
        { dueAmount: Number(parseFloat(bookingCursor.dueAmount).toFixed(2)) + Number(parseFloat(billAmount).toFixed(2)) }
      );
      if (!updateBooking) throw new customError(ERR_MSG.BILL_NOT_ATTACHED, ERR_CODE.INTERNAL);
    }

    // find bill
    const condition = { hotel: hotelId, _id: addObject.id };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// add a document of a collection
const addRoomBill = async (hotelId, employeeId, bookingId) => {
  const FUNCTION_NAME = "addRoomBill";

  let cGSTAmount = 0;
  let sGSTAmount = 0;
  let billAmount = 0;
  let totalAmount = 0;

  try {
    // find booking
    const bookingCondition = { _id: bookingId, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);
    const billNo = parseInt(hotelCursor.lastFinalBillNo) + 1;

    bookingCursor.details.room.map((room) => {
      totalAmount = parseFloat(totalAmount).toFixed(2) + parseFloat(room.tariff).toFixed(2);
      cGSTAmount = parseFloat(cGSTAmount).toFixed(2) + parseFloat(room.cGSTAmount).toFixed(2);
      sGSTAmount = parseFloat(sGSTAmount).toFixed(2) + parseFloat(room.sGSTAmount).toFixed(2);
    });

    billAmount = parseFloat(totalAmount).toFixed(2) +
      parseFloat(cGSTAmount).toFixed(2) +
      parseFloat(sGSTAmount).toFixed(2);

    // update last food bill no in hotel document
    await Hotel.findByIdAndUpdate(hotelId, { lastFinalBillNo: billNo });

    // insert bill data to bill document
    const addBillData = new Bill({
      hotel: hotelId,
      employee: employeeId,
      booking: _id,
      type: ORDER_TYPE.ROOM,
      no: billNo,
      totalAmount: totalAmount,
      cGSTAmount: cGSTAmount,
      sGSTAmount: sGSTAmount,
      billAmount: billAmount,
      status: TABLE_STATUS.ATTACHED
    });
    const addBillObject = await addBillData.save();
    if (!addBillObject) throw new customError(ERR_MSG.BILL_NOT_SAVE, ERR_CODE.INTERNAL);

    // find bill
    const condition = { hotel: hotelId, _id: addBillObject.id };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// add a document of a collection
const addOtherBill = async (hotelId, employeeId, orderId, isAttach = true) => {
  const FUNCTION_NAME = "addOtherBill";

  let addData = null;
  let foodCGSTAmount = 0;
  let foodSGSTAmount = 0;
  let serviceCGSTAmount = 0;
  let serviceSGSTAmount = 0;
  let serviceChargeAmount = 0;
  let billAmount = 0;
  let totalAmount = 0;
  let billNo = 0;

  try {
    // check for duplicate data in db
    const billCondition = { hotel: hotelId, order: orderId };
    const billCursor = await Bill.findOne(billCondition);
    if (billCursor) throw new customError(ERR_MSG.BILL_CONFLICT, ERR_CODE.NOT_EXISTS);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);

    let foodBillNo = Number(parseInt(hotelCursor.lastFoodBillNo));
    let serviceBillNo = Number(parseInt(hotelCursor.lastServiceBillNo));
    let miscellaneousBillNo = Number(parseInt(hotelCursor.lastMiscellaneousBillNo));
    const foodCGSTPercentage = Number(parseFloat(hotelCursor.foodCGSTPercentage).toFixed(2));
    const foodSGSTPercentage = Number(parseFloat(hotelCursor.foodSGSTPercentage).toFixed(2));
    const serviceCGSTPercentage = Number(parseFloat(hotelCursor.serviceCGSTPercentage).toFixed(2));
    const serviceSGSTPercentage = Number(parseFloat(hotelCursor.serviceSGSTPercentage).toFixed(2));
    const serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));

    // find order
    const coderCondition = { hotel: hotelId, _id: orderId };
    const orderCursor = await Order.findOne(coderCondition);

    orderCursor.tokens.map((token) => {
      token.items.map((item) => {
        totalAmount = Number(parseFloat(totalAmount).toFixed(2)) +
          (Number(parseFloat(item.unitPrice).toFixed(2)) * Number(parseInt(item.quantity)));
      });
    });

    switch (orderCursor.type) {
      case ORDER_TYPE.FOOD:
        foodBillNo = parseInt(foodBillNo) + 1;
        billNo = parseInt(foodBillNo);
        foodCGSTAmount = (Number(parseFloat(foodCGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        foodSGSTAmount = (Number(parseFloat(foodSGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        billAmount = Number(parseFloat(totalAmount).toFixed(2)) +
          Number(parseFloat(foodCGSTAmount).toFixed(2)) +
          Number(parseFloat(foodSGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceChargeAmount).toFixed(2));

        // insert bill data to bill document
        addData = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          tables: orderCursor.tables,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          cGSTPercentage: Number(parseFloat(foodCGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(foodCGSTAmount).toFixed(2)),
          sGSTPercentage: Number(parseFloat(foodSGSTPercentage).toFixed(2)),
          sGSTAmount: Number(parseFloat(foodSGSTAmount).toFixed(2)),
          serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
          serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
          billAmount: Number(parseFloat(billAmount).toFixed(0)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      case ORDER_TYPE.SERVICE:
        serviceBillNo = parseInt(serviceBillNo) + 1;
        billNo = parseInt(serviceBillNo);
        serviceCGSTAmount = (Number(parseFloat(serviceCGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceSGSTAmount = (Number(parseFloat(serviceSGSTPercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) / 100) * Number(parseFloat(totalAmount).toFixed(2));
        billAmount = Number(parseFloat(totalAmount).toFixed(2)) +
          Number(parseFloat(serviceCGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceSGSTAmount).toFixed(2)) +
          Number(parseFloat(serviceChargeAmount).toFixed(2));

        // insert bill data to bill document
        addData = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          cGSTPercentage: Number(parseFloat(serviceCGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(serviceCGSTAmount).toFixed(2)),
          sGSTPercentage: Number(parseFloat(serviceSGSTPercentage).toFixed(2)),
          sGSTAmount: Number(parseFloat(serviceSGSTAmount).toFixed(2)),
          serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
          serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
          billAmount: Number(parseFloat(billAmount).toFixed(0)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      case ORDER_TYPE.MISCELLANEOUS:
        miscellaneousBillNo = parseInt(miscellaneousBillNo) + 1;
        billNo = parseInt(miscellaneousBillNo);
        billAmount = Number(parseFloat(totalAmount).toFixed(2));

        // insert bill data to bill document
        addData = new Bill({
          hotel: hotelId,
          employee: employeeId,
          booking: orderCursor.booking,
          order: orderId,
          type: orderCursor.type,
          no: billNo,
          totalAmount: Number(parseFloat(totalAmount).toFixed(2)),
          billAmount: Number(parseFloat(billAmount).toFixed(0)),
          status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED
        });

        break;

      default:
        break;
    }

    // update last food bill no in hotel document
    const modObject = await Hotel.findByIdAndUpdate(hotelId, {
      lastFoodBillNo: foodBillNo,
      lastServiceBillNo: serviceBillNo,
      lastMiscellaneousBillNo: miscellaneousBillNo
    });
    if (!modObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    // add bill
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    // update order
    const updateOrder = await Order.findByIdAndUpdate(orderId, {
      status: orderCursor.booking ? isAttach ? TABLE_STATUS.ATTACHED : TABLE_STATUS.BILLED : TABLE_STATUS.BILLED,
    });
    if (!updateOrder) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    if (orderCursor.tables) {
      orderCursor.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.BILLED });
      });
    }

    if (orderCursor.booking && isAttach) {
      // find booking
      const bookingCursor = await Booking.findOne({ hotel: hotelId, _id: orderCursor.booking });
      if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

      // update booking
      const modBookingObject = await Booking.updateMany(
        { hotel: hotelId, _id: orderCursor.booking },
        { dueAmount: Number(parseFloat(bookingCursor.dueAmount).toFixed(2)) + Number(parseFloat(billAmount).toFixed(2)) }
      );
      if (!modBookingObject) throw new customError(ERR_MSG.BILL_NOT_ATTACHED, ERR_CODE.INTERNAL);
    }

    // find bill
    const condition = { _id: addObject.id, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// mod a document of a collection
const modRoomAttachedBill = async (hotelId, employeeId, bookingId) => {
  const FUNCTION_NAME = "modRoomAttachedBill";

  try {
    // find bill
    const billCondition = { hotel: hotelId, booking: bookingId };
    let billCursor = await Bill.findOne(billCondition);
    if (!billCursor) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // find booking
    const bookingCondition = { _id: bookingId, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BILL_CONFLICT, ERR_CODE.NOT_EXISTS);

    let totalAmount = 0;
    let totalGSTAmount = 0;

    bookingCursor.rooms.map((room) => {
      totalAmount = parseFloat(totalAmount).toFixed(2) + parseFloat(room.tariff).toFixed(2);
      totalGSTAmount = parseFloat(totalGSTAmount).toFixed(2) +
        parseFloat(room.cGSTAmount).toFixed(2) +
        parseFloat(room.sGSTAmount).toFixed(2);
    });

    const billAmount = parseFloat(totalAmount).toFixed(2) + parseFloat(totalGSTAmount).toFixed(2);

    // update bill
    const updateBillObject = await Bill.findByIdAndUpdate(billCursor.id, {
      employee: employeeId,
      order: bookingId,
      type: ORDER_TYPE.ROOM,
      totalAmount: parseFloat(totalAmount).toFixed(2),
      cGSTPercentage: parseFloat(cGSTPercentage).toFixed(2),
      cGSTAmount: parseFloat(cGSTAmount).toFixed(2),
      sGSTPercentage: parseFloat(sGSTPercentage).toFixed(2),
      sGSTAmount: parseFloat(sGSTAmount).toFixed(2),
      serviceChargeAmount: parseFloat(serviceChargeAmount).toFixed(2),
      billAmount: parseFloat(billAmount).toFixed(2),
      status: TABLE_STATUS.BILLED
    });
    if (!updateBillObject) throw new customError(ERR_MSG.BILL_NOT_SAVE, ERR_CODE.INTERNAL);

    // update booking
    const updateBookingObject = await Booking.findByIdAndUpdate(bookingId, { status: ROOM_STATUS.BILLED });
    if (!updateBookingObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    // find bill
    const cursor = await Bill.findOne(billCondition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // return output
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// mod a document of a collection
const modOtherBill = async (hotelId, employeeId, orderId) => {
  const FUNCTION_NAME = "modOtherBill";

  try {
    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCluster = await Hotel.findOne(hotelCondition);
    if (!hotelCluster) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const foodCGSTPercentage = parseFloat(hotelCluster.foodCGSTPercentage).toFixed(2);
    const foodSGSTPercentage = parseFloat(hotelCluster.foodSGSTPercentage).toFixed(2);
    const serviceChargePercentage = parseFloat(hotelCluster.serviceChargePercentage).toFixed(2);

    // find bill
    const billCondition = { order: orderId, hotel: hotelId };
    const billCluster = await Bill.findOne(billCondition);
    if (!billCluster) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // find order
    const orderCondition = { _id: orderId, hotel: hotelId };
    const orderCursor = await Order.findOne(orderCondition);
    if (!orderCursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    let totalAmount = 0;

    orderCursor.tokens.map((token) => {
      token.items.map((item) => {
        totalAmount = parseFloat(totalAmount).toFixed(2) +
          (parseFloat(item.unitPrice).toFixed(2) * parseInt(item.quantity));
      });
    });

    let cGSTAmount = 0;
    let sGSTAmount = 0;
    let billAmount = 0;
    const serviceChargeAmount = (parseFloat(serviceChargePercentage).toFixed(2) / 100) * parseFloat(totalAmount).toFixed(2);

    switch (orderCursor.type) {
      case ORDER_TYPE.FOOD:
        cGSTAmount = (parseFloat(foodCGSTPercentage).toFixed(2) / 100) * parseFloat(totalAmount).toFixed(2);
        sGSTAmount = (parseFloat(foodSGSTPercentage).toFixed(2) / 100) * parseFloat(totalAmount).toFixed(2);
        billAmount = parseFloat(totalAmount).toFixed(2) +
          parseFloat(cGSTAmount).toFixed(2) +
          parseFloat(sGSTAmount).toFixed(2) +
          parseFloat(serviceChargeAmount).toFixed(2);

        break;

      case ORDER_TYPE.SERVICE:
        billAmount = parseFloat(totalAmount).toFixed(2) + parseFloat(serviceChargeAmount).toFixed(2);

        break;

      case ORDER_TYPE.MISCELLANEOUS:

        billAmount = parseFloat(totalAmount).toFixed(2) + parseFloat(serviceChargeAmount).toFixed(2);

        break;

      default:
        break;
    }

    // update bill
    const modObject = await Bill.findByIdAndUpdate(billCluster.id, {
      employee: employeeId,
      order: _id,
      type: orderCursor.type,
      totalAmount: parseFloat(totalAmount).toFixed(2),
      cGSTPercentage: parseFloat(foodCGSTPercentage).toFixed(2),
      cGSTAmount: parseFloat(cGSTAmount).toFixed(2),
      sGSTPercentage: parseFloat(foodSGSTPercentage).toFixed(2),
      sGSTAmount: parseFloat(sGSTAmount).toFixed(2),
      serviceChargeAmount: parseFloat(serviceChargeAmount).toFixed(2),
      billAmount: parseFloat(billAmount).toFixed(2),
      status: TABLE_STATUS.BILLED
    });
    if (!modObject) throw new customError(ERR_MSG.BILL_NOT_SAVE, ERR_CODE.INTERNAL);

    // update table
    if (orderCursor.type === ORDER_TYPE.FOOD) {
      orderCursor.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.BILLED });
      });
    }

    // find bill
    const condition = { _id: billCluster.id, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};



// get all documents of a collection
const mongoBills = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoBills";

  try {
    const condition = { hotel: hotelId };
    const order = { no: 1 };
    const cursor = await Bill.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        order: object.order.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchBill = async (hotelId, employeeId, no, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchBill";

  try {
    // find bill
    let condition = null;
    if (no === "") condition = { hotel: hotelId, updatedAt: { $gte: startDate, $lte: endDate } };
    else condition = { hotel: hotelId, no: no, updatedAt: { $gte: startDate, $lte: endDate } };
    const order = { no: 1 };
    const cursor = await Bill.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        order: object.order.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetBill = async (hotelId, employeeId, billId) => {
  const FUNCTION_NAME = "mongoGetBill";

  try {
    const condition = { _id: billId, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoGenerateBill = async (hotelId, employeeId, orderId, type, isAttach) => {
  const FUNCTION_NAME = "mongoGenerateBill";

  try {
    const condition = { _id: orderId, hotel: hotelId };
    cursor = await Order.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    if (type === ORDER_TYPE.ROOM) {
      return addRoomBill(hotelId, employeeId, orderId);
    } else {
      if (cursor.booking) {
        return addBill(hotelId, employeeId, orderId, isAttach);
      }
      else if (cursor.tables.length) {
        return addOtherBill(hotelId, employeeId, orderId, true);
      }
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoReGenerateBill = async (hotelId, employeeId, bookingId) => {
  const FUNCTION_NAME = "mongoReGenerateBill";

  try {
    // find booking
    const bookingCondition = { _id: bookingId, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (bookingCursor) return modRoomAttachedBill(hotelId, employeeId, bookingId);

    // find order
    const orderCondition = { _id: bookingCursor.order, hotel: hotelId };
    const orderCursor = await Order.findOne(orderCondition);
    if (orderCursor) return modOtherBill(hotelId, employeeId, bookingCursor.order);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelBill = async (hotelId, employeeId, billId) => {
  const FUNCTION_NAME = "mongoDelBill";

  try {
    // find bill
    const condition = { _id: billId, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    // delete bill
    const delObject = await Bill.deleteOne({ _id: billId, hotel: hotelId });
    if (!delObject) throw new customError(ERR_MSG.BILL_NOT_DELETE, ERR_CODE.INTERNAL);

    // update order
    await Order.findByIdAndUpdate(cursor.order, { status: TABLE_STATUS.ORDERED });

    if (cursor.type === ORDER_TYPE.FOOD) {
      // find order
      const orderCursor = await Order.findOne({ _id: cursor.order, hotel: hotelId });

      // update table
      orderCursor.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.DELIVERED });
      });
    }

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all documents of a collection
const mongoFinalBills = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoFinalBills";

  try {
    const condition = {
      hotel: hotelId,
      billNo: { $ne: null },
      updatedAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
    };
    const order = { billNo: 1, startDate: 1 };
    const cursor = await Booking.find(condition).sort(order);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        })
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  mongoBills, mongoSearchBill, mongoGetBill, mongoGenerateBill,
  mongoReGenerateBill, mongoDelBill, mongoFinalBills
};