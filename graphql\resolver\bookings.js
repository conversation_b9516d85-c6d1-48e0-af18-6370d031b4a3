const { subDays } = require("date-fns");
const Room = require("../../models/room");
const { mongoGetAgent } = require("./mongo/agentMongo");
const { mongoGetPlan } = require("./mongo/planMongo");
const { redisGetAgent } = require("./redis/agentRedis");
const { redisGetPlan } = require("./redis/planRedis");
const { mongoGetIdentification } = require("./mongo/identificationMongo");
const { redisGetIdentification } = require("./redis/identificationRedis");
const { mongoAddGuest, mongoModGuest } = require("./mongo/guestMongo");
const { mongoBookings, mongoSearchBooking, mongoGetBooking, mongoAddBooking, mongoModBooking,
  mongoDelBooking, mongoGetCheckout, mongoRoomSales } = require("./mongo/bookingMongo");
const { setAllRoomRedis } = require("../../helpers/redis/room");
const { isAuthorized } = require("../../helpers/authorize");
const { bookingDetail, roomSalesDetail, checkoutDetail } = require("../../helpers/db");
const { stringToDbDate } = require("../../helpers/date");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "bookings.js";

const PopulateRoom = async (hotelId) => {
  try {
    const condition = { hotel: hotelId, isEnable: true };
    const order = { no: 1 };
    const cursor = await Room.find(condition).sort(order);
    const data = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString()
      };
    });

    await setAllRoomRedis(hotelId, data);
  } catch (error) {
    throw new Error(error.message, ERR_CODE.INTERNAL);
  }
  finally {
    console.log("Room populated in redis");
  }
};

// get all documents of a collection
const bookings = async (args, req) => {
  const FUNCTION_NAME = "bookings";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoBookings(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await bookingDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchBooking = async (args, req) => {
  const FUNCTION_NAME = "searchBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.bookingSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    const startDateInput =
      startDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(endDate)), 1).toISOString();

    // search data
    const searchObjects = await mongoSearchBooking(hotelId, employeeId, startDateInput, endDateInput);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await bookingDetail(object);
    });
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getBooking = async (args, req) => {
  const FUNCTION_NAME = "getBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    const getObject = await mongoGetBooking(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_GET);

    // return output
    return await bookingDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addBooking = async (args, req) => {
  const FUNCTION_NAME = "addBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { guestName, guestAddress, guestCity, guestPoliceStation, guestState, guestPin,
    guestMobile, guestEmail, guestFather, guestAge, guestCount, guestMaleCount, guestFemaleCount,
    guestChildCount, guestIdentification, guestIdNo, company, companyAddress, companyGstNo,
    plan, agent, rooms } = args.bookingInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (guestName === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (guestAddress === "") throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.BAD_REQUEST);
    if (guestCity === "") throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);
    if (guestPoliceStation === "") throw new customError(ERR_MSG.INVALID_PS, ERR_CODE.BAD_REQUEST);
    if (guestState === "") throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.BAD_REQUEST);
    if (guestPin === "") throw new customError(ERR_MSG.INVALID_PIN, ERR_CODE.BAD_REQUEST);
    if (guestMobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (guestFather === "") throw new customError(ERR_MSG.INVALID_FATHER, ERR_CODE.BAD_REQUEST);
    if (guestAge <= 0) throw new customError(ERR_MSG.INVALID_AGE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestMaleCount < 0) throw new customError(ERR_MSG.INVALID_MALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestFemaleCount < 0) throw new customError(ERR_MSG.INVALID_FEMALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestChildCount < 0) throw new customError(ERR_MSG.INVALID_CHILD_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestIdentification === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (guestIdNo === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (plan === "") throw new customError(ERR_MSG.INVALID_PLAN, ERR_CODE.BAD_REQUEST);
    if (agent === "") throw new customError(ERR_MSG.INVALID_AGENT, ERR_CODE.BAD_REQUEST);

    //check if plan exists
    let planObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      planObject = await mongoGetPlan(hotelId, employeeId, plan);
    } else {
      planObject = await redisGetPlan(hotelId, employeeId, plan);
    }
    if (!planObject) throw new customError(ERR_MSG.PLAN_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check if agent exists
    let agentObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      agentObject = await mongoGetAgent(hotelId, employeeId, agent);
    } else {
      agentObject = await redisGetAgent(hotelId, employeeId, agent);
    }
    if (!agentObject) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check for identification exists
    let identificationObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      identificationObject = await mongoGetIdentification(hotelId, employeeId, guestIdentification);
    } else {
      identificationObject = await redisGetIdentification(hotelId, employeeId, guestIdentification);
    }
    if (!identificationObject) throw new customError(ERR_MSG.IDENTIFICATION_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const _name = guestName.trim().toUpperCase();
    const _address = guestAddress.trim().toUpperCase();
    const _city = guestCity.trim().toUpperCase();
    const _policeStation = guestPoliceStation.trim().toUpperCase();
    const _state = guestState.trim().toUpperCase();
    const _pin = guestPin.trim();
    const _mobile = guestMobile.trim();
    const _email = guestEmail.trim().toLowerCase();
    const _father = guestFather.trim().toUpperCase();
    const _age = guestAge;
    const _guestCount = guestCount;
    const _maleCount = guestMaleCount;
    const _femaleCount = guestFemaleCount;
    const _childCount = guestChildCount;
    const _identification = guestIdentification.trim();
    const _idNo = guestIdNo.trim().toUpperCase();
    const _company = company.trim().toUpperCase();
    const _companyAddress = companyAddress.trim().toUpperCase();
    const _companyGstNo = companyGstNo.trim().toUpperCase();
    const _plan = planObject._id;
    const _agent = agentObject._id;

    // add guest
    const addGuestObject = await mongoAddGuest(hotelId, employeeId, _name, _address, _city, _policeStation, _state,
      _pin, _mobile, _email, _father, _age, _guestCount, _maleCount, _femaleCount, _childCount, _identification, _idNo,
      _company, _companyAddress, _companyGstNo);

    // add booking
    const addBookingObject = await mongoAddBooking(hotelId, employeeId, addGuestObject._id, _plan, _agent, rooms);

    // populate room in redis 
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateRoom(hotelId);

    // write all to redis
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_ADD);

    // return output
    return await bookingDetail(addBookingObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modBooking = async (args, req) => {
  const FUNCTION_NAME = "modBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, guestId, guestName, guestAddress, guestCity, guestPoliceStation, guestState, guestPin,
    guestMobile, guestEmail, guestFather, guestAge, guestCount, guestMaleCount, guestFemaleCount,
    guestChildCount, guestIdentification, guestIdNo, company, companyAddress, companyGstNo,
    plan, agent, rooms } = args.bookingInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!guestId) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (guestName === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (guestAddress === "") throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.BAD_REQUEST);
    if (guestCity === "") throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);
    if (guestPoliceStation === "") throw new customError(ERR_MSG.INVALID_PS, ERR_CODE.BAD_REQUEST);
    if (guestState === "") throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.BAD_REQUEST);
    if (guestPin === "") throw new customError(ERR_MSG.INVALID_PIN, ERR_CODE.BAD_REQUEST);
    if (guestMobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (guestFather === "") throw new customError(ERR_MSG.INVALID_FATHER, ERR_CODE.BAD_REQUEST);
    if (guestAge <= 0) throw new customError(ERR_MSG.INVALID_AGE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestMaleCount < 0) throw new customError(ERR_MSG.INVALID_MALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestFemaleCount < 0) throw new customError(ERR_MSG.INVALID_FEMALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestChildCount < 0) throw new customError(ERR_MSG.INVALID_CHILD_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (guestIdentification === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (guestIdNo === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (plan === "") throw new customError(ERR_MSG.INVALID_PLAN, ERR_CODE.BAD_REQUEST);
    if (agent === "") throw new customError(ERR_MSG.INVALID_AGENT, ERR_CODE.BAD_REQUEST);

    //check if plan exists
    let planObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      planObject = await mongoGetPlan(hotelId, employeeId, plan);
    } else {
      planObject = await redisGetPlan(hotelId, employeeId, plan);
    }
    if (!planObject) throw new customError(ERR_MSG.PLAN_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check if agent exists
    let agentObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      agentObject = await mongoGetAgent(hotelId, employeeId, agent);
    } else {
      agentObject = await redisGetAgent(hotelId, employeeId, agent);
    }
    if (!agentObject) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check for identification exists
    let identificationObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      identificationObject = await mongoGetIdentification(hotelId, employeeId, guestIdentification);
    } else {
      identificationObject = await redisGetIdentification(hotelId, employeeId, guestIdentification);
    }
    if (!identificationObject) throw new customError(ERR_MSG.IDENTIFICATION_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const _name = guestName.trim().toUpperCase();
    const _address = guestAddress.trim().toUpperCase();
    const _city = guestCity.trim().toUpperCase();
    const _policeStation = guestPoliceStation.trim().toUpperCase();
    const _state = guestState.trim().toUpperCase();
    const _pin = guestPin.trim();
    const _mobile = guestMobile.trim();
    const _email = guestEmail.trim().toLowerCase();
    const _father = guestFather.trim().toUpperCase();
    const _age = guestAge;
    const _guestCount = guestCount;
    const _maleCount = guestMaleCount;
    const _femaleCount = guestFemaleCount;
    const _childCount = guestChildCount;
    const _identification = guestIdentification.trim();
    const _idNo = guestIdNo.trim().toUpperCase();
    const _company = company.trim().toUpperCase();
    const _companyAddress = companyAddress.trim().toUpperCase();
    const _companyGstNo = companyGstNo.trim().toUpperCase();
    const _plan = planObject._id;
    const _agent = agentObject._id;

    // modify guest data
    const modGuestObject = await mongoModGuest(hotelId, employeeId, guestId, _name, _address, _city, _policeStation, _state,
      _pin, _mobile, _email, _father, _age, _guestCount, _maleCount, _femaleCount, _childCount, _identification, _idNo,
      _company, _companyAddress, _companyGstNo);

    // modify booking data
    const modBookingObject = await mongoModBooking(hotelId, employeeId, _id, modGuestObject._id, _plan, _agent, rooms);

    // populate room in redis 
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateRoom(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_MOD);

    // return output
    return await bookingDetail(modBookingObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delBooking = async (args, req) => {
  const FUNCTION_NAME = "delBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // delete data
    const delObject = await mongoDelBooking(hotelId, employeeId, _id);

    // populate room in redis 
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateRoom(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BOOKING_DEL);

    // return output
    return await bookingDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all room sales for a period
const roomSales = async (args, req) => {
  const FUNCTION_NAME = "roomSales";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.roomSalesSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const startDateInput =
      startDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(endDate)), 1).toISOString()

    // sales data
    const saleObjects = await mongoRoomSales(hotelId, employeeId, startDateInput, endDateInput);
    if (!saleObjects) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_SALES);

    // return output
    return saleObjects.map((object) => {
      return roomSalesDetail(object);
    });
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all room sales for a period
const getCheckout = async (args, req) => {
  const FUNCTION_NAME = "getCheckout";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchDate = args.dateInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const _searchDate =
      searchDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(searchDate)), 1).toISOString();

    // get all data
    const allObjects = await mongoGetCheckout(hotelId, employeeId, _searchDate);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.CHECK_OUT);

    // return output
    return allObjects.map((object) => {
      return checkoutDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { bookings, searchBooking, getBooking, addBooking, modBooking, delBooking, roomSales, getCheckout };