const { subDays } = require("date-fns");
const Table = require("../../models/table");
const { setAllTableRedis } = require("../../helpers/redis/table");
const { mongoOrders, mongoSearchOrder, mongoGetOrder, mongoAddOrder, mongoModOrder,
  mongoDelOrder, mongoModOrderStatus } = require("./mongo/orderMongo");
const { orderDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToDbDate } = require("../../helpers/date");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ORDER_TYPE, ORDER_STATUS } = require("../../configs/orderOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "orders.js";

const PopulateTable = async (hotelId) => {
  try {
    const condition = { hotel: hotelId, isEnable: true };
    const order = { no: 1 };
    const cursor = await Table.find(condition).sort(order);

    const data = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString()
      };
    });

    await setAllTableRedis(hotelId, data);
  } catch (error) {
    throw new Error(error.message, ERR_CODE.INTERNAL);
  }
  finally {
    console.log("Table populated in redis");
  }
};

// get all documents of a collection
const orders = async (args, req) => {
  const FUNCTION_NAME = "orders";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { type, status } = args.ordersInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    if (!type) throw new customError(ERR_MSG.INVALID_TYPE, ERR_CODE.BAD_REQUEST);
    if (!status) throw new customError(ERR_MSG.INVALID_STATUS, ERR_CODE.BAD_REQUEST);

    // get all data
    const allObjects = await mongoOrders(hotelId, employeeId, type, status);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchOrder = async (args, req) => {
  const FUNCTION_NAME = "searchOrder";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { type, status, startDate, endDate } = args.orderSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const startDateInput =
      startDate === ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate === ""
        ? new Date()
        : new Date(stringToDbDate(endDate)).toISOString();

    // search data
    const searchObjects = await mongoSearchOrder(hotelId, employeeId, type, status, startDateInput, endDateInput);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await orderDetail(object)
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getOrder = async (args, req) => {
  const FUNCTION_NAME = "getOrder";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, tokenId } = args.tokenInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get data
    const getObject = await mongoGetOrder(hotelId, employeeId, _id, tokenId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_GET);

    // return output
    return getObject.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addOrder = async (args, req) => {
  const FUNCTION_NAME = "addOrder";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, type, booking, tables, employee, deliveryDate, deliveryTime, items } = args.orderInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (type.trim().toUpperCase() !== ORDER_TYPE.FOOD &&
      type.trim().toUpperCase() !== ORDER_TYPE.SERVICE &&
      type.trim().toUpperCase() !== ORDER_TYPE.MISCELLANEOUS) {
      throw new customError(ERR_MSG.INVALID_TYPE, ERR_CODE.BAD_REQUEST);
    }
    if (!employee) throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.CONFLICT);
    if (!deliveryDate) throw new customError(ERR_MSG.DELIVERY_DATE_NOT_EXISTS, ERR_CODE.CONFLICT);
    if (!deliveryTime) throw new customError(ERR_MSG.DELIVERY_TIME_NOT_EXISTS, ERR_CODE.CONFLICT);
    if (items === undefined) throw new customError(ERR_MSG.INVALID_FOOD, ERR_CODE.BAD_REQUEST);

    // add document
    const addObject = await mongoAddOrder(hotelId, employeeId, _id, type, booking, tables,
      employee, deliveryDate, deliveryTime, items);

    // populate redis table
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateTable(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_DEL);

    // return output
    return addObject.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modOrder = async (args, req) => {
  const FUNCTION_NAME = "modOrder";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, booking, tables, tokenId, employee, deliveryDate, deliveryTime, items } = args.orderInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!tokenId) throw new customError(ERR_MSG.INVALID_TOKEN_ID, ERR_CODE.CONFLICT);
    if (!employee) throw new customError(ERR_MSG.INVALID_EMPLOYEE, ERR_CODE.CONFLICT);
    if (!deliveryDate) throw new customError(ERR_MSG.DELIVERY_DATE_NOT_EXISTS, ERR_CODE.CONFLICT);
    if (!deliveryTime) throw new customError(ERR_MSG.DELIVERY_TIME_NOT_EXISTS, ERR_CODE.CONFLICT);
    if (items === undefined) throw new customError(ERR_MSG.INVALID_FOOD, ERR_CODE.BAD_REQUEST);

    // update document 
    const modObject = await mongoModOrder(hotelId, employeeId, _id, tokenId, booking, tables, employee,
      deliveryDate, deliveryTime, items);

    // populate redis table
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateTable(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_MOD);

    // return output
    return modObject.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delOrder = async (args, req) => {
  const FUNCTION_NAME = "delOrder";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, tokenId } = args.tokenInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (!tokenId) throw new customError(ERR_MSG.INVALID_TOKEN_ID, ERR_CODE.BAD_REQUEST);

    // update document 
    const delObject = await mongoDelOrder(hotelId, employeeId, _id, tokenId);

    // populate redis table
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateTable(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_DEL);

    // return output
    return delObject.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modOrderStatus = async (args, req) => {
  const FUNCTION_NAME = "modOrderStatus";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, tokenId, orderItemIds, status } = args.updateOrderStatusInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ORDER, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.NOT_EXISTS);
    if (!tokenId) throw new customError(ERR_MSG.TOKEN_ID_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    if (orderItemIds === undefined) throw new customError(ERR_MSG.INVALID_FOOD, ERR_CODE.BAD_REQUEST);
    if (status.trim().toUpperCase() !== ORDER_STATUS.ORDERED &&
      status.trim().toUpperCase() !== ORDER_STATUS.DELIVERED &&
      status.trim().toUpperCase() !== ORDER_STATUS.BILLED &&
      status.trim().toUpperCase() !== ORDER_STATUS.PAID &&
      status.trim().toUpperCase() !== ORDER_STATUS.ATTACHED) {
      throw new customError(ERR_MSG.ORDER_STATUS_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    }

    // update document 
    const modObject = await mongoModOrderStatus(hotelId, employeeId, _id, tokenId, orderItemIds, status);

    // populate redis table
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) PopulateTable(hotelId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ORDER_STATUS_CHANGE);

    // return output
    return modObject.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { orders, searchOrder, getOrder, addOrder, modOrder, delOrder, modOrderStatus };