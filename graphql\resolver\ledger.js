const Booking = require("../../models/booking");
const { ledgerDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "ledger.js";

// find a document by id from a collection
const getLedger = async (args, req) => {
  const FUNCTION_NAME = "getLedger";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.LEDGER, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get booking
    const bookingObject = await Booking.findOne({ _id: _id, hotel: hotelId });
    if (!bookingObject) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const spread = {
      ...bookingObject._doc,
      _id: bookingObject.id,
      hotel: bookingObject.hotel.toString(),
      employees: await Promise.all(bookingObject.employees.map(async (employee) => employee.toString())),
      guest: bookingObject.guest.toString(),
      plan: bookingObject.plan.toString(),
      agent: bookingObject.agent.toString(),
      rooms: bookingObject.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.LEDGER_GET);

    // return output
    return await ledgerDetail(spread);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { getLedger };
