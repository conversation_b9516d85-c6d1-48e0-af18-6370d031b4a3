const mongoose = require("mongoose");
const Agent = require("../../../models/agent");
const { processTextToEmbedding } = require("../../../helpers/embedding");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "agentMongoAI.js";

// Search within collection
const mongoAISearchAgent = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoAISearchAgent";

  try {
    if (searchKey === "") {
      const condition = { hotel: hotelId, isEnable: true };
      const order = { name: 1 };
      const cursor = await Agent.find(condition).sort(order);

      // spread data
      const spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
      });

      return spread;
    }
    else {
      // get data
      // const searchEmbedding = await createEmbedding(searchKey);
      const searchEmbedding = await processTextToEmbedding(searchKey);
      const pipeline = [
        {
          $vectorSearch: {
            index: process.env.DB_AGENT_INDEX_VECTOR,
            path: 'embedding',
            queryVector: searchEmbedding,
            numCandidates: 10,
            limit: 10,
            filter: {
              $and: [
                { hotel: { $eq: new mongoose.Types.ObjectId(hotelId) } },
                { isEnable: { $eq: true } }
              ]
            }
          }
        },
        {
          $project: {
            _id: 1,
            hotel: 1,
            name: 1,
            description: 1,
            score: { $meta: 'vectorSearchScore' }
          }
        }
      ];
      const cursor = await Agent.aggregate(pipeline).exec();

      // spread data
      const spread = cursor
        .filter((object) => object.score >= 0.55)
        .map((object) => {
          return {
            _id: object._id.toString(),
            hotel: object.hotel.toString(),
            name: object.name,
            description: object.description,
            score: object.score
          };
        });

      return spread;
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Insert a document into the collection
const mongoAIAddAgent = async (hotelId, employeeId, name, description) => {
  const FUNCTION_NAME = "mongoAIAddAgent";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Agent.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.AGENT_CONFLICT, ERR_CODE.CONFLICT);

    // add data in vector db
    const summary = `Agent '${name}'${description ? `, ${description}` : ''}.`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // insert data in db
    const data = {
      hotel: hotelId,
      name: name,
      description: description,
      embedding: embedding
    };
    const addData = new Agent(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.AGENT_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Agent.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Modify a document of a collection
const mongoAIModAgent = async (hotelId, employeeId, id, name, description) => {
  const FUNCTION_NAME = "mongoAIModAgent";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Agent.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.AGENT_CONFLICT, ERR_CODE.CONFLICT);

    // add data in vector db
    const summary = `Agent '${name}'${description ? `, ${description}` : ''}.`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // change data in db
    const modData = {
      name: name,
      description: description,
      embedding: embedding
    };
    const modObject = await Agent.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.AGENT_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Agent.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { mongoAISearchAgent, mongoAIAddAgent, mongoAIModAgent };