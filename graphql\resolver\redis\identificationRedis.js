const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneIdentificationRedis } = require("../../../helpers/redis/identification");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "identificationRedis.js";

// get all documents of a collection
const redisIdentifications = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisIdentifications";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ID_CARD_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '50'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchIdentification = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchIdentification";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ID_CARD_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '50'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetIdentification = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetIdentification";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ID_CARD_UNIQUE}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetIdentification = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetIdentification";

  try {
    // read single data
    await setOneIdentificationRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelIdentification = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelIdentification";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_IDENTIFICATION}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  redisIdentifications, redisSearchIdentification, redisGetIdentification,
  redisSetIdentification, redisDelIdentification
};