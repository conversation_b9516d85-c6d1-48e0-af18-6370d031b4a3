const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOnePaymentModeRedis } = require("../../../helpers/redis/paymentMode");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "paymentModeRedis.js";

// get all documents of a collection
const redisPaymentModes = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisPaymentModes";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_PAYMENT_MODE_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '50'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchPaymentMode = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchPaymentMode";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_PAYMENT_MODE_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '50'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetPaymentMode = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetPaymentMode";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_PAYMENT_MODE_UNIQUE}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetPaymentMode = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetPaymentMode";

  try {
    // read single data
    await setOnePaymentModeRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelPaymentMode = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelPaymentMode";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_PAYMENT_MODE}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  redisPaymentModes, redisSearchPaymentMode, redisGetPaymentMode,
  redisSetPaymentMode, redisDelPaymentMode
};