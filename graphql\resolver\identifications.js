const { mongoIdentifications, mongoSearchIdentification, mongoGetIdentification, mongoAddIdentification, mongoModIdentification, mongoDelIdentification, mongoDelIdentifications } = require("./mongo/identificationMongo");
const { redisIdentifications, redisSearchIdentification, redisGetIdentification, redisSetIdentification, redisDelIdentification } = require("./redis/identificationRedis");
const { identificationDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "identifications.js";

// get all documents of a collection
const identifications = async (args, req) => {
  const FUNCTION_NAME = "identifications";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObject = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) allObject = await mongoIdentifications(hotelId, employeeId);
    else allObject = await redisIdentifications(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_LIST);

    // return output
    return allObject.map(async (object) => {
      return await identificationDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchIdentification = async (args, req) => {
  const FUNCTION_NAME = "searchIdentification";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.identificationSearchInput ? args.identificationSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) searchObjects = await mongoSearchIdentification(hotelId, employeeId, searchKey);
    else searchObjects = await redisSearchIdentification(hotelId, employeeId, searchKey);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await identificationDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getIdentification = async (args, req) => {
  const FUNCTION_NAME = "getIdentification";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) getObject = await mongoGetIdentification(hotelId, employeeId, _id);
    else getObject = await redisGetIdentification(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.IDENTIFICATION_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_GET);

    // return output
    return await identificationDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addIdentification = async (args, req) => {
  const FUNCTION_NAME = "addIdentification";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, description } = args.identificationInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddIdentification(hotelId, employeeId, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetIdentification(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_ADD);

    // return output
    return await identificationDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modIdentification = async (args, req) => {
  const FUNCTION_NAME = "modIdentification";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, description } = args.identificationInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModIdentification(hotelId, employeeId, _id, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetIdentification(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_MOD);

    // return output
    return await identificationDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delIdentification = async (args, req) => {
  const FUNCTION_NAME = "delIdentification";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelIdentification(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelIdentification(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ID_DEL);

    // return output
    return await identificationDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delIdentifications = async (args, req) => {
  const FUNCTION_NAME = "delIdentifications";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.identificationsInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ID, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelIdentifications(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelIdentification(hotelId, employeeId, object._id);
      })
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.IDES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await identificationDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  identifications, searchIdentification, getIdentification, addIdentification,
  modIdentification, delIdentification, delIdentifications
};