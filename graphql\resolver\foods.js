const { mongoFoods, mongoSearchFood, mongoGetFood, mongoAddFood, mongoModFood, mongoDelFood, mongoDelFoods } = require("./mongo/foodMongo");
const { redisFoods, redisSearchFood, redisGetFood, redisSetFood, redisDelFood } = require("./redis/foodRedis");
const { foodDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "foods.js";

// get all documents of a collection
const foods = async (args, req) => {
  const FUNCTION_NAME = "foods";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoFoods(hotelId, employeeId);
    }
    else {
      allObjects = await redisFoods(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await foodDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchFood = async (args, req) => {
  const FUNCTION_NAME = "searchFood";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.foodSearchInput ? args.foodSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchFood(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchFood(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await foodDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getFood = async (args, req) => {
  const FUNCTION_NAME = "getFood";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetFood(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetFood(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.FOOD_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_GET);

    // return output
    return await foodDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addFood = async (args, req) => {
  const FUNCTION_NAME = "addFood";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, unitPrice, description } = args.foodInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PRICE, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddFood(hotelId, employeeId, _name, _unitPrice, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetFood(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_ADD);

    // return output
    return await foodDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modFood = async (args, req) => {
  const FUNCTION_NAME = "modFood";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, unitPrice, description } = args.foodInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PRICE, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModFood(hotelId, employeeId, _id, _name, _unitPrice, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetFood(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_MOD);

    // return output
    return await foodDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delFood = async (args, req) => {
  const FUNCTION_NAME = "delFood";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelFood(hotelId, employeeId, _id);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelFood(hotelId, delObject);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOOD_DEL);

    // return output
    return await foodDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delFoods = async (args, req) => {
  const FUNCTION_NAME = "delFoods";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.foodsInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.FOOD, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelFoods(hotelId, employeeId, _ids[0].split(","));

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelFood(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.FOODS_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await foodDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { foods, searchFood, getFood, addFood, modFood, delFood, delFoods };