const { mongoRoomCategories, mongoSearchRoomCategory, mongoGetRoomCategory, mongoAddRoomCategory, mongoModRoomCategory, mongoDelRoomCategory, mongoDelRoomCategories } = require("./mongo/roomCategoryMongo");
const { redisRoomCategories, redisSearchRoomCategory, redisGetRoomCategory, redisSetRoomCategory, redisDelRoomCategory } = require("./redis/roomCategoryRedis");
const { roomCategoryDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "roomCategories.js";

// get all documents of a collection
const roomCategories = async (args, req) => {
  const FUNCTION_NAME = "roomCategories";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoRoomCategories(hotelId, employeeId);
    }
    else {
      allObjects = await redisRoomCategories(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await roomCategoryDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchRoomCategory = async (args, req) => {
  const FUNCTION_NAME = "searchRoomCategory";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.roomCategorySearchInput ? args.roomCategorySearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchRoomCategory(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchRoomCategory(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await roomCategoryDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getRoomCategory = async (args, req) => {
  const FUNCTION_NAME = "getRoomCategory";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;
  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetRoomCategory(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetRoomCategory(hotelId, employeeId, _id);
    }
    if (!getObject) throw new customError(ERR_MSG.CATEGORY_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_GET);

    // return output
    return await roomCategoryDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addRoomCategory = async (args, req) => {
  const FUNCTION_NAME = "addRoomCategory";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, accommodation, tariff, maxDiscount, extraBedTariff, extraPersonTariff, description } = args.roomCategoryInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (tariff === "") throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (tariff < 0) throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (maxDiscount === "") throw new customError(ERR_MSG.INVALID_DISCOUNT, ERR_CODE.BAD_REQUEST);
    if (maxDiscount < 0) throw new customError(ERR_MSG.INVALID_DISCOUNT, ERR_CODE.BAD_REQUEST);
    if (extraBedTariff === "") throw new customError(ERR_MSG.INVALID_EXTRA_BED_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraBedTariff < 0) throw new customError(ERR_MSG.INVALID_EXTRA_BED_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraPersonTariff === "") throw new customError(ERR_MSG.INVALID_EXTRA_PERSON_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraPersonTariff < 0) throw new customError(ERR_MSG.INVALID_EXTRA_PERSON_TARIFF, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _accommodation = accommodation;
    const _tariff = tariff;
    const _maxDiscount = maxDiscount ? maxDiscount : 0;
    const _extraBedTariff = extraBedTariff ? extraBedTariff : 0;
    const _extraPersonTariff = extraPersonTariff ? extraPersonTariff : 0;
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddRoomCategory(hotelId, employeeId, _name, _accommodation, _tariff,
      _maxDiscount, _extraBedTariff, _extraPersonTariff, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRoomCategory(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_ADD);

    // return output
    return await roomCategoryDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modRoomCategory = async (args, req) => {
  const FUNCTION_NAME = "modRoomCategory";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, accommodation, tariff, maxDiscount, extraBedTariff, extraPersonTariff, description } = args.roomCategoryInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (tariff === "") throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (tariff < 0) throw new customError(ERR_MSG.INVALID_TARIFF, ERR_CODE.BAD_REQUEST);
    if (maxDiscount === "") throw new customError(ERR_MSG.INVALID_DISCOUNT, ERR_CODE.BAD_REQUEST);
    if (maxDiscount < 0) throw new customError(ERR_MSG.INVALID_DISCOUNT, ERR_CODE.BAD_REQUEST);
    if (extraBedTariff === "") throw new customError(ERR_MSG.INVALID_EXTRA_BED_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraBedTariff < 0) throw new customError(ERR_MSG.INVALID_EXTRA_BED_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraPersonTariff === "") throw new customError(ERR_MSG.INVALID_EXTRA_PERSON_TARIFF, ERR_CODE.BAD_REQUEST);
    if (extraPersonTariff < 0) throw new customError(ERR_MSG.INVALID_EXTRA_PERSON_TARIFF, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _accommodation = accommodation;
    const _tariff = tariff;
    const _maxDiscount = maxDiscount ? maxDiscount : 0;
    const _extraBedTariff = extraBedTariff ? extraBedTariff : 0;
    const _extraPersonTariff = extraPersonTariff ? extraPersonTariff : 0;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModRoomCategory(hotelId, employeeId, _id, _name, _accommodation, _tariff,
      _maxDiscount, _extraBedTariff, _extraPersonTariff, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRoomCategory(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_MOD);

    // return output
    return await roomCategoryDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delRoomCategory = async (args, req) => {
  const FUNCTION_NAME = "delRoomCategory";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelRoomCategory(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelRoomCategory(hotelId, employeeId, delObject._id);

    // write all to redis
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORY_DEL);

    // return output
    return await roomCategoryDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delRoomCategories = async (args, req) => {
  const FUNCTION_NAME = "delRoomCategories";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.roomCategoriesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.ROOM_CATEGORY, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelRoomCategories(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelRoomCategory(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROOM_CATEGORIES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await roomCategoryDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  roomCategories, searchRoomCategory, getRoomCategory, addRoomCategory,
  modRoomCategory, delRoomCategory, delRoomCategories
};