const { mongoServices, mongoSearchService, mongoGetService, mongoAddService,
  mongoModService, mongoDelService, mongoDelServices } = require("./mongo/serviceMongo");
const { redisServices, redisSearchService, redisGetService, redisSetService, redisDelService } = require("./redis/serviceRedis");
const { serviceDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "services.js";

// get all documents of a collection
const services = async (args, req) => {
  const FUNCTION_NAME = "services";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoServices(hotelId, employeeId);
    }
    else {
      allObjects = await redisServices(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await serviceDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchService = async (args, req) => {
  const FUNCTION_NAME = "searchService";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.serviceSearchInput ? args.serviceSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchService(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchService(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await serviceDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getService = async (args, req) => {
  const FUNCTION_NAME = "getService";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetService(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetService(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.SERVICE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_GET);

    // return output
    return await serviceDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addService = async (args, req) => {
  const FUNCTION_NAME = "addService";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, unitPrice, description } = args.serviceInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PRICE, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddService(hotelId, employeeId, _name, _unitPrice, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetService(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_ADD);

    // return output
    return await serviceDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modService = async (args, req) => {
  const FUNCTION_NAME = "modService";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, unitPrice, description } = args.serviceInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PRICE, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModService(hotelId, employeeId, _id, _name, _unitPrice, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetService(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_MOD);

    // return output
    return await serviceDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delService = async (args, req) => {
  const FUNCTION_NAME = "delService";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelService(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelService(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICE_DEL);

    // return output
    return await serviceDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delServices = async (args, req) => {
  const FUNCTION_NAME = "delServices";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.servicesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.SERVICE, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelServices(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelService(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.SERVICES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await serviceDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { services, searchService, getService, addService, modService, delService, delServices };