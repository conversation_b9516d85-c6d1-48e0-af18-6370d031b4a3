const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneEmployeeRedis } = require("../../../helpers/redis/employee");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "employeeRedis.js";

// get all documents of a collection
const redisEmployees = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisEmployees";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_EMPLOYEE_FILTER}`,
      query,
      'RETURN', '7', '_id', 'hotel', 'role', 'name', 'address', 'mobile', 'email',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchEmployee = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchEmployee";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_EMPLOYEE_FILTER}`,
      query,
      'RETURN', '7', '_id', 'hotel', 'role', 'name', 'address', 'mobile', 'email',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.mobile) ||
          regex.test(item.email) ||
          regex.test(item.address));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const redisGetEmployee = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetEmployee";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_EMPLOYEE_UNIQUE}`,
      query,
      'RETURN', '7', '_id', 'hotel', 'role', 'name', 'address', 'mobile', 'email',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetEmployee = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetEmployee";

  try {
    // read single data
    await setOneEmployeeRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelEmployee = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelEmployee";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_EMPLOYEE}_${hotelId}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisEmployees, redisSearchEmployee, redisGetEmployee, redisSetEmployee, redisDelEmployee };