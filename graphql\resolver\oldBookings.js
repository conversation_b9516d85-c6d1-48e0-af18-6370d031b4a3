const { subDays } = require("date-fns");
const { mongoOldBookings, mongoSearchOldBooking } = require("./mongo/oldBookingMongo");
const { bookingDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { stringToDbDate } = require("../../helpers/date");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "oldBookings.js";

// get all documents of a collection
const oldBookings = async (args, req) => {
  const FUNCTION_NAME = "oldBookings";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoOldBookings(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await bookingDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchOldBooking = async (args, req) => {
  const FUNCTION_NAME = "searchOldBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.bookingSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const startDateInput =
      startDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(endDate)), 1).toISOString();

    // search data
    const searchObjects = await mongoSearchOldBooking(hotelId, employeeId, startDateInput, endDateInput);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await bookingDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { oldBookings, searchOldBooking };