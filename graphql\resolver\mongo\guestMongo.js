const Guest = require("../../../models/guest");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "guestMongo.js";

// get all documents of a collection
const mongoGuests = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoGuests";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { updatedAt: 1, name: 1 };
    const cursor = await Guest.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        identification: object.identification ? object.identification.toString() : null
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchGuest = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchGuest";
  const regex = new RegExp(searchKey, "i");

  try {
    // read all documents from db
    const condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    const order = { name: 1 };
    const cursor = await Guest.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        identification: object.identification ? object.identification.toString() : null
      }
    });

    // filter array
    const data = spread.filter(item => regex.test(item.name));

    return data;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetGuest = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetGuest";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Guest.findOne(condition);
    if (cursor) spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      identification: cursor.identification ? cursor.identification.toString() : null
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddGuest = async (hotelId, employeeId, name, address, city, policeStation, state, pin,
  mobile, email, father, age, guestCount, maleCount, femaleCount, childCount, identification, idNo,
  company, companyAddress, companyGstNo) => {
  const FUNCTION_NAME = "mongoAddGuest";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, mobile: mobile, email: email, createdAt: { $eq: new Date() }, isEnable: true };
    const duplicateCursor = await Guest.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.GUEST_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const addData = new Guest({
      hotel: hotelId,
      employee: employeeId,
      name: name,
      address: address,
      city: city,
      policeStation: policeStation,
      state: state,
      pin: pin,
      mobile: mobile,
      email: email,
      father: father,
      age: age,
      guestCount: guestCount,
      maleCount: maleCount,
      femaleCount: femaleCount,
      childCount: childCount,
      identification: identification,
      idNo: idNo,
      company: company,
      companyAddress: companyAddress,
      companyGstNo: companyGstNo
    });
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.GUEST_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Guest.findOne(findCondition);
    const spread = {
      ...findCursor._doc,
      _id: findCursor.id,
      hotel: findCursor.hotel.toString(),
      employee: findCursor.employee.toString(),
      identification: findCursor.identification ? findCursor.identification.toString() : null
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModGuest = async (hotelId, employeeId, id, name, address, city, policeStation, state, pin,
  mobile, email, father, age, guestCount, maleCount, femaleCount, childCount, identification, idNo,
  company, companyAddress, companyGstNo) => {
  const FUNCTION_NAME = "mongoModGuest";

  try {
    // check for duplicate data in db
    const condition = {
      _id: { $not: { $eq: id } }, hotel: hotelId, name: name, mobile: mobile, email: email,
      createdAt: { $eq: new Date() }, isEnable: true
    };
    const duplicateCursor = await Guest.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.GUEST_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = {
      employee: employeeId,
      name: name,
      address: address,
      city: city,
      policeStation: policeStation,
      state: state,
      pin: pin,
      mobile: mobile,
      email: email,
      father: father,
      age: age,
      guestCount: guestCount,
      maleCount: maleCount,
      femaleCount: femaleCount,
      childCount: childCount,
      identification: identification,
      idNo: idNo,
      company: company,
      companyAddress: companyAddress,
      companyGstNo: companyGstNo
    };
    const modObject = await Guest.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.GUEST_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Guest.findOne(findCondition);
    const spread = {
      ...findCursor._doc,
      _id: findCursor.id,
      hotel: findCursor.hotel.toString(),
      employee: findCursor.employee.toString(),
      identification: findCursor.identification ? findCursor.identification.toString() : null
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelGuest = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelGuest";

  try {
    // check for existence
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Guest.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.GUEST_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      identification: cursor.identification ? cursor.identification.toString() : null
    };

    // delete from db
    const delObject = await Guest.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.EMPLOYEE_NOT_DELETE, ERR_CODE.INTERNAL);

    // return output
    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoGuests, mongoSearchGuest, mongoGetGuest, mongoAddGuest, mongoModGuest, mongoDelGuest };